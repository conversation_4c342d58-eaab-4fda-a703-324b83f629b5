list=[{
"base": &"Node",
"class": &"AISystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Advanced/AISystem.gd"
}, {
"base": &"Control",
"class": &"AdvancedSystemsUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/AdvancedSystemsUI.gd"
}, {
"base": &"CharacterBody2D",
"class": &"Animal",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Entities/Animal.gd"
}, {
"base": &"Node",
"class": &"AnimalSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Environment/AnimalSystem.gd"
}, {
"base": &"Node2D",
"class": &"Building",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Entities/Building.gd"
}, {
"base": &"CharacterBody2D",
"class": &"Colonist",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Entities/Colonist.gd"
}, {
"base": &"Node",
"class": &"CombatSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Colonist/CombatSystem.gd"
}, {
"base": &"Node",
"class": &"ContentManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Managers/ContentManager.gd"
}, {
"base": &"RefCounted",
"class": &"DataGenerator",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Utils/DataGenerator.gd"
}, {
"base": &"Node",
"class": &"DataManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Managers/DataManager.gd"
}, {
"base": &"Control",
"class": &"DataVisualizationUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/DataVisualizationUI.gd"
}, {
"base": &"Node",
"class": &"EquipmentSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Colonist/EquipmentSystem.gd"
}, {
"base": &"Control",
"class": &"EventLogUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/EventLogUI.gd"
}, {
"base": &"Node",
"class": &"EventSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Social/EventSystem.gd"
}, {
"base": &"Node",
"class": &"FactionSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Social/FactionSystem.gd"
}, {
"base": &"RefCounted",
"class": &"GameConfig",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Core/GameConfig.gd"
}, {
"base": &"Node",
"class": &"GameInitializer",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Core/GameInitializer.gd"
}, {
"base": &"CanvasLayer",
"class": &"GameUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/GameUI.gd"
}, {
"base": &"Node",
"class": &"GeneticSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Advanced/GeneticSystem.gd"
}, {
"base": &"Node2D",
"class": &"GridOverlay",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/GridOverlay.gd"
}, {
"base": &"Control",
"class": &"LanguageSettingsUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/LanguageSettingsUI.gd"
}, {
"base": &"Node",
"class": &"MedicalSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Colonist/MedicalSystem.gd"
}, {
"base": &"Node",
"class": &"MoodSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Colonist/MoodSystem.gd"
}, {
"base": &"Control",
"class": &"MultiLanguageDemo",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Demo/MultiLanguageDemo.gd"
}, {
"base": &"Node",
"class": &"PollutionSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Environment/PollutionSystem.gd"
}, {
"base": &"Node",
"class": &"PowerSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Environment/PowerSystem.gd"
}, {
"base": &"Node",
"class": &"ReligionSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Social/ReligionSystem.gd"
}, {
"base": &"Node",
"class": &"ResearchSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Technology/ResearchSystem.gd"
}, {
"base": &"Control",
"class": &"ResearchUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/ResearchUI.gd"
}, {
"base": &"Node",
"class": &"RoomSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Environment/RoomSystem.gd"
}, {
"base": &"Node",
"class": &"SeasonSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Environment/SeasonSystem.gd"
}, {
"base": &"CharacterBody2D",
"class": &"SimpleColonist",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Entities/SimpleColonist.gd"
}, {
"base": &"CanvasLayer",
"class": &"SimpleGameUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/SimpleGameUI.gd"
}, {
"base": &"Control",
"class": &"SimpleUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/SimpleUI.gd"
}, {
"base": &"Node",
"class": &"StorytellerSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Advanced/StorytellerSystem.gd"
}, {
"base": &"Node",
"class": &"SystemManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Core/SystemManager.gd"
}, {
"base": &"Node",
"class": &"TestMultiLanguage",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Demo/TestMultiLanguage.gd"
}, {
"base": &"Node",
"class": &"TestRunner",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Test/TestRunner.gd"
}, {
"base": &"Control",
"class": &"TestSceneController",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Demo/TestSceneController.gd"
}, {
"base": &"Node",
"class": &"TradeSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Social/TradeSystem.gd"
}, {
"base": &"Control",
"class": &"TradeUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/UI/TradeUI.gd"
}, {
"base": &"RefCounted",
"class": &"Utils",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Utils/Utils.gd"
}, {
"base": &"Node",
"class": &"VehicleSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Advanced/VehicleSystem.gd"
}, {
"base": &"Node",
"class": &"WorkSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Systems/Colonist/WorkSystem.gd"
}, {
"base": &"Node2D",
"class": &"WorldMap",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Entities/WorldMap.gd"
}]
