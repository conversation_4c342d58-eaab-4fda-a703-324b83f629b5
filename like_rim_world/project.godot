; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="RimWorld-like Colony Simulator"
config/description="A complex colony management game with deep systems and multilingual support"
run/main_scene="res://scenes/Main.tscn"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="uid://dsh4v1bhvoxba"

[autoload]

LocalizationManager="*res://scripts/Managers/LocalizationManager.gd"
GameManager="*res://scripts/Core/GameManager.gd"

[display]

window/size/viewport_width=1280
window/size/viewport_height=720

[input]

ui_left_click={
"deadzone": 0.5,
"events": [Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"button_mask":1,"position":Vector2(0, 0),"global_position":Vector2(0, 0),"factor":1.0,"button_index":1,"canceled":false,"pressed":false,"double_click":false,"script":null)
]
}
ui_right_click={
"deadzone": 0.5,
"events": [Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"button_mask":2,"position":Vector2(0, 0),"global_position":Vector2(0, 0),"factor":1.0,"button_index":2,"canceled":false,"pressed":false,"double_click":false,"script":null)
]
}

[internationalization]

locale/test=false

[layer_names]

2d_physics/layer_1="World"
2d_physics/layer_2="Colonists"
2d_physics/layer_3="Buildings"
2d_physics/layer_4="Resources"

[rendering]

textures/canvas_textures/default_texture_filter=0
