[gd_scene load_steps=3 format=3 uid="uid://d06bwt2wbotd6"]

[ext_resource type="Script" uid="uid://3j1w6xhkm0iw" path="res://scripts/Test/GridSystemTestController.gd" id="1_0x8y9"]
[ext_resource type="Script" path="res://scripts/UI/GridOverlay.gd" id="2_1a2b3"]

[node name="GridSystemTest" type="Node2D"]
script = ExtResource("1_0x8y9")

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(2, 2)

[node name="GridOverlay" type="Node2D" parent="."]
script = ExtResource("2_1a2b3")

[node name="UI" type="CanvasLayer" parent="."]

[node name="TestPanel" type="Panel" parent="UI"]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -200.0
offset_right = 400.0
offset_bottom = -10.0

[node name="VBoxContainer" type="VBoxContainer" parent="UI/TestPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "网格系统测试"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2

[node name="GridToggle" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "切换网格显示 (G)"

[node name="OccupationToggle" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "切换占用显示 (H)"

[node name="PlaceBuilding" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "放置建筑 (鼠标左键)"

[node name="RemoveBuilding" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "移除建筑 (鼠标右键)"

[node name="ClearAll" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "清除所有建筑"

[node name="HSeparator2" type="HSeparator" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2

[node name="InfoLabel" type="Label" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "WASD: 移动相机
鼠标滚轮: 缩放
ESC: 退出测试"
vertical_alignment = 1

[node name="Buildings" type="Node2D" parent="."]
