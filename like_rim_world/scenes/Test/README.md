# 🧪 RimWorld-like 游戏测试系统

## 📋 概述

完整的测试系统，用于测试游戏的各个功能模块和系统集成。

## 🚀 快速开始

### 方法1: 从主游戏启动
1. 运行主游戏场景 `scenes/Main.tscn`
2. 按 **T** 键打开测试菜单
3. 选择要测试的系统

### 方法2: 直接运行测试
在Godot编辑器中直接运行以下场景：
- `scenes/Test/TestMenu.tscn` - 测试菜单
- `scenes/Test/GridSystemTest.tscn` - 网格系统测试
- `scenes/Test/ColonistSystemTest.tscn` - 殖民者系统测试
- `scenes/Test/GameSystemsTest.tscn` - 游戏系统测试
- `scenes/Test/AllSystemsTest.tscn` - 全系统综合测试

## 🎮 测试项目

### 1. 网格系统测试 (GridSystemTest)
**功能**: 测试32x32网格对齐建筑系统
- ✅ 网格可视化显示/隐藏
- ✅ 建筑精确对齐到网格
- ✅ 建筑放置冲突检测
- ✅ 建筑移除功能
- ✅ 相机控制和缩放

**控制**:
- `WASD`: 移动相机
- `鼠标滚轮`: 缩放
- `G`: 切换网格显示
- `H`: 切换占用状态显示
- `鼠标左键`: 放置建筑
- `鼠标右键`: 移除建筑
- `ESC`: 退出测试

### 2. 殖民者系统测试 (ColonistSystemTest)
**功能**: 测试殖民者生成、管理和交互
- ✅ 单个/批量殖民者生成
- ✅ 殖民者选择和信息显示
- ✅ 健康、心情、技能系统测试
- ✅ 实时统计信息更新

**控制**:
- `WASD`: 移动相机
- `鼠标左键`: 选择殖民者
- `ESC`: 退出测试

### 3. 游戏系统测试 (GameSystemsTest)
**功能**: 测试各个游戏子系统
- ✅ 医疗系统测试
- ✅ 战斗系统测试
- ⚠️ 工作、心情、研究、贸易、事件、派系系统测试 (待实现)
- ✅ 详细的测试日志和结果报告

### 4. 全系统综合测试 (AllSystemsTest)
**功能**: 完整游戏环境的综合测试
- ✅ 所有实体类型的生成和管理
- ✅ 网格系统集成
- ✅ 测试场景模拟（袭击、贸易、事件）
- ✅ 实时系统监控

**控制**:
- `WASD`: 移动相机
- `鼠标滚轮`: 缩放
- `G`: 切换网格显示
- `U`: 切换UI显示
- `鼠标左键`: 选择/放置实体
- `鼠标右键`: 移除实体
- `ESC`: 退出测试

## 🔧 技术特性

### 网格系统
- **32x32像素网格**: 所有建筑完美对齐
- **冲突检测**: 防止建筑重叠
- **多格建筑**: 支持占用多个网格的大型建筑
- **可视化**: 网格线和占用状态显示

### 实体系统
- **殖民者**: 完整的属性系统（健康、心情、技能）
- **建筑**: 网格对齐的建筑放置系统
- **动物**: AI驱动的动物行为系统

### 测试框架
- **自动化测试**: TestRunner支持批量测试
- **详细日志**: 彩色日志、时间戳、测试结果
- **交互式测试**: 鼠标和键盘操作
- **性能监控**: 实时系统状态监控

## 📊 测试结果

测试系统会自动记录：
- ✅ 测试通过/失败状态
- ⏱️ 测试执行时间
- 📝 详细的错误日志
- 📈 系统性能指标

## 🐛 故障排除

### 常见问题

**Q: 测试场景无法加载**
A: 确保所有依赖文件存在，特别是：
- `scripts/Entities/Animal.gd`
- `scripts/Entities/Building.gd`
- `scripts/Entities/Colonist.gd`

**Q: 网格不显示**
A: 按 `G` 键切换网格显示，或检查GridOverlay组件

**Q: 建筑无法放置**
A: 检查网格位置是否被占用，红色区域表示已占用

**Q: 测试按钮无响应**
A: 检查控制台错误信息，可能是脚本连接问题

## 🚀 扩展测试

### 添加新测试
1. 在 `scripts/Test/` 创建新的测试控制器
2. 在 `scenes/Test/` 创建对应的测试场景
3. 在 `TestMenu.tscn` 添加新的测试按钮
4. 更新 `TestRunner.gd` 包含新测试

### 自定义测试场景
```gdscript
# 示例：创建自定义测试
extends Node2D

func _ready():
    print("自定义测试开始")
    # 添加测试逻辑
    
func run_custom_test():
    # 实现测试功能
    pass
```

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的网格系统测试
- ✅ 殖民者系统测试
- ✅ 基础游戏系统测试
- ✅ 全系统综合测试
- ✅ 测试菜单和导航
- ✅ 动物系统实现
- ✅ 快捷键支持 (T键打开测试菜单)

---

**提示**: 所有测试都支持 `ESC` 键快速退出，返回主菜单或上一级界面。
