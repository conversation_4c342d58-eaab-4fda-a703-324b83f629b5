[gd_scene load_steps=3 format=3 uid="uid://sddgg74yxwor"]

[ext_resource type="Script" uid="uid://frueh2uw8c1u" path="res://scripts/Test/AllSystemsTestController.gd" id="1_3l4m5"]
[ext_resource type="Script" path="res://scripts/UI/GridOverlay.gd" id="2_4n5o6"]

[node name="AllSystemsTest" type="Node2D"]
script = ExtResource("1_3l4m5")

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(1.5, 1.5)

[node name="World" type="Node2D" parent="."]

[node name="GridOverlay" type="Node2D" parent="World"]
script = ExtResource("2_4n5o6")

[node name="Colonists" type="Node2D" parent="World"]

[node name="Buildings" type="Node2D" parent="World"]

[node name="Animals" type="Node2D" parent="World"]

[node name="UI" type="CanvasLayer" parent="."]

[node name="TopPanel" type="Panel" parent="UI"]
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 100.0

[node name="HBoxContainer" type="HBoxContainer" parent="UI/TopPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="UI/TopPanel/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "全系统综合测试"
horizontal_alignment = 1
vertical_alignment = 1

[node name="VSeparator" type="VSeparator" parent="UI/TopPanel/HBoxContainer"]
layout_mode = 2

[node name="StatsContainer" type="VBoxContainer" parent="UI/TopPanel/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ColonistStats" type="Label" parent="UI/TopPanel/HBoxContainer/StatsContainer"]
layout_mode = 2
text = "殖民者: 0"

[node name="BuildingStats" type="Label" parent="UI/TopPanel/HBoxContainer/StatsContainer"]
layout_mode = 2
text = "建筑: 0"

[node name="SystemStats" type="Label" parent="UI/TopPanel/HBoxContainer/StatsContainer"]
layout_mode = 2
text = "系统状态: 正常"

[node name="LeftPanel" type="Panel" parent="UI"]
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 10.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = 200.0

[node name="VBoxContainer" type="VBoxContainer" parent="UI/LeftPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="TestControls" type="Label" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "测试控制"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2

[node name="SpawnColonist" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "生成殖民者"

[node name="PlaceBuilding" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "放置建筑"

[node name="SpawnAnimal" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "生成动物"

[node name="HSeparator2" type="HSeparator" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2

[node name="TestScenarios" type="Label" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "测试场景"
horizontal_alignment = 1

[node name="TestRaid" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "模拟袭击"

[node name="TestTrade" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "模拟贸易"

[node name="TestEvent" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "触发事件"

[node name="HSeparator3" type="HSeparator" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2

[node name="DisplayControls" type="Label" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "显示控制"
horizontal_alignment = 1

[node name="ToggleGrid" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "切换网格 (G)"

[node name="ToggleUI" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "切换UI (U)"

[node name="HSeparator4" type="HSeparator" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2

[node name="ExitTest" type="Button" parent="UI/LeftPanel/VBoxContainer"]
layout_mode = 2
text = "退出测试"

[node name="RightPanel" type="Panel" parent="UI"]
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = -10.0
offset_bottom = 200.0

[node name="VBoxContainer" type="VBoxContainer" parent="UI/RightPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="LogTitle" type="Label" parent="UI/RightPanel/VBoxContainer"]
layout_mode = 2
text = "系统日志"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="UI/RightPanel/VBoxContainer"]
layout_mode = 2

[node name="LogContainer" type="ScrollContainer" parent="UI/RightPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="LogText" type="RichTextLabel" parent="UI/RightPanel/VBoxContainer/LogContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
bbcode_enabled = true
text = "[color=green]全系统测试准备就绪[/color]"

[node name="BottomPanel" type="Panel" parent="UI"]
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -80.0

[node name="HBoxContainer" type="HBoxContainer" parent="UI/BottomPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="InfoLabel" type="Label" parent="UI/BottomPanel/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "WASD: 移动相机 | 鼠标滚轮: 缩放 | 鼠标左键: 选择/放置 | 鼠标右键: 移除 | G: 网格 | U: UI | ESC: 退出"
horizontal_alignment = 1
vertical_alignment = 1
