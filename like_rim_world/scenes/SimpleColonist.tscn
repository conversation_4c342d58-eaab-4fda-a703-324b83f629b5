[gd_scene load_steps=3 format=3 uid="uid://bqxvwxb8ixqyb"]

[ext_resource type="Script" uid="uid://bu3bllg0pvcg8" path="res://scripts/Entities/SimpleColonist.gd" id="1_simple_colonist"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(32, 48)

[node name="SimpleColonist" type="CharacterBody2D"]
script = ExtResource("1_simple_colonist")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0, -24)
shape = SubResource("RectangleShape2D_1")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.5, 0.8, 1, 1)
position = Vector2(0, -24)

[node name="NameLabel" type="Label" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -40.0
offset_right = 50.0
offset_bottom = -20.0
text = "Colonist"
horizontal_alignment = 1
