[gd_scene load_steps=3 format=3 uid="uid://bpvoitce62d1f"]

[ext_resource type="Script" uid="uid://dschgwf07x3pj" path="res://scripts/Controllers/MainSceneController.gd" id="1_main_controller"]
[ext_resource type="Script" uid="uid://dhsa5fdq8k6au" path="res://scripts/UI/SimpleGameUI.gd" id="2_gameui"]

[node name="Main" type="Node2D"]
script = ExtResource("1_main_controller")

[node name="GameManager" type="Node" parent="."]

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(0.8, 0.8)

[node name="UI" type="CanvasLayer" parent="."]
script = ExtResource("2_gameui")

[node name="Background" type="ColorRect" parent="UI"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
color = Color(0.2, 0.3, 0.2, 1)

[node name="TopPanel" type="Panel" parent="UI"]
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 80.0

[node name="ResourceContainer" type="HBoxContainer" parent="UI/TopPanel"]
layout_mode = 1
anchors_preset = 9
anchor_bottom = 1.0
offset_left = 10.0
offset_right = 600.0
grow_vertical = 2
alignment = 1

[node name="FoodLabel" type="Label" parent="UI/TopPanel/ResourceContainer"]
layout_mode = 2
text = "🍖 食物: 100"

[node name="WoodLabel" type="Label" parent="UI/TopPanel/ResourceContainer"]
layout_mode = 2
text = "🪵 木材: 50"

[node name="StoneLabel" type="Label" parent="UI/TopPanel/ResourceContainer"]
layout_mode = 2
text = "🪨 石材: 30"

[node name="SteelLabel" type="Label" parent="UI/TopPanel/ResourceContainer"]
layout_mode = 2
text = "⚙️ 钢材: 10"

[node name="MedicineLabel" type="Label" parent="UI/TopPanel/ResourceContainer"]
layout_mode = 2
text = "💊 药品: 5"

[node name="TimeContainer" type="HBoxContainer" parent="UI/TopPanel"]
layout_mode = 1
anchors_preset = 11
anchor_left = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -300.0
offset_right = -10.0
grow_horizontal = 0
grow_vertical = 2
alignment = 2

[node name="PauseButton" type="Button" parent="UI/TopPanel/TimeContainer"]
layout_mode = 2
text = "⏸️"

[node name="Speed1Button" type="Button" parent="UI/TopPanel/TimeContainer"]
layout_mode = 2
text = "1x"

[node name="Speed2Button" type="Button" parent="UI/TopPanel/TimeContainer"]
layout_mode = 2
text = "2x"

[node name="Speed3Button" type="Button" parent="UI/TopPanel/TimeContainer"]
layout_mode = 2
text = "3x"

[node name="TimeLabel" type="Label" parent="UI/TopPanel/TimeContainer"]
layout_mode = 2
text = "时间: 0天"

[node name="BottomPanel" type="Panel" parent="UI"]
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -120.0

[node name="ButtonContainer" type="HBoxContainer" parent="UI/BottomPanel"]
layout_mode = 1
anchors_preset = 9
anchor_bottom = 1.0
offset_left = 10.0
offset_right = 800.0
grow_vertical = 2
alignment = 1

[node name="BuildButton" type="Button" parent="UI/BottomPanel/ButtonContainer"]
layout_mode = 2
text = "🏗️ 建造"

[node name="ResearchButton" type="Button" parent="UI/BottomPanel/ButtonContainer"]
layout_mode = 2
text = "🔬 研究"

[node name="TradeButton" type="Button" parent="UI/BottomPanel/ButtonContainer"]
layout_mode = 2
text = "💰 贸易"

[node name="SystemsButton" type="Button" parent="UI/BottomPanel/ButtonContainer"]
layout_mode = 2
text = "⚙️ 系统"

[node name="LanguageButton" type="Button" parent="UI/BottomPanel/ButtonContainer"]
layout_mode = 2
text = "🌐 语言"

[node name="InfoPanel" type="Panel" parent="UI"]
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -150.0
offset_bottom = 150.0

[node name="InfoContainer" type="VBoxContainer" parent="UI/InfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="InfoTitle" type="Label" parent="UI/InfoPanel/InfoContainer"]
layout_mode = 2
text = "信息面板"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="UI/InfoPanel/InfoContainer"]
layout_mode = 2

[node name="InfoText" type="RichTextLabel" parent="UI/InfoPanel/InfoContainer"]
layout_mode = 2
size_flags_vertical = 3
text = "欢迎来到 RimWorld-like 游戏！

点击按钮来体验各种功能：
• 建造 - 建造建筑物
• 研究 - 解锁新技术
• 贸易 - 与商人交易
• 系统 - 查看高级系统
• 语言 - 切换游戏语言"

[node name="World" type="Node2D" parent="."]

[node name="Ground" type="Node2D" parent="World"]

[node name="Buildings" type="Node2D" parent="World"]

[node name="Colonists" type="Node2D" parent="World"]

[node name="Animals" type="Node2D" parent="World"]
