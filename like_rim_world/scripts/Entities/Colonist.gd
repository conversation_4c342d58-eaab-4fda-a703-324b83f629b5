extends CharacterBody2D
class_name Colonist

# 殖民者类 - RimWorld风格的角色系统

signal health_changed(new_health: float)
signal mood_changed(new_mood: float)
signal task_changed(new_task: String)
signal skill_improved(skill_name: String, new_level: int)

# 基本属性
@export var colonist_name: String = "Unknown"
@export var age: int = 25
@export var gender: String = "neutral"

# 健康系统
@export var max_health: float = 100.0
@export var current_health: float = 100.0
@export var is_injured: bool = false
@export var is_sick: bool = false

# 心情系统
@export var current_mood: float = 50.0
@export var base_mood: float = 50.0

# 需求系统
var needs: Dictionary = {
	"hunger": 100.0,
	"rest": 100.0,
	"recreation": 50.0,
	"comfort": 50.0,
	"beauty": 50.0,
	"space": 50.0
}

# 技能系统
var skills: Dictionary = {
	"shooting": 5,
	"melee": 5,
	"construction": 5,
	"mining": 5,
	"cooking": 5,
	"plants": 5,
	"animals": 5,
	"crafting": 5,
	"artistic": 5,
	"medical": 5,
	"social": 5,
	"intellectual": 5
}

# 特性系统
var traits: Array[String] = []
var backstory: Dictionary = {}

# AI和任务系统
@export var current_task: String = "idle"
@export var task_priority: int = 0
var task_target: Vector2 = Vector2.ZERO
var task_object: Node = null

# 装备系统
var equipped_weapon: Node = null
var equipped_apparel: Array[Node] = []

# 移动系统
@export var move_speed: float = 100.0
var target_position: Vector2 = Vector2.ZERO
var is_moving: bool = false

# 视觉组件
@onready var sprite: Sprite2D = $Sprite2D
@onready var name_label: Label = $NameLabel
@onready var health_bar: ProgressBar = $HealthBar
@onready var mood_indicator: ColorRect = $MoodIndicator

func _ready():
	# 初始化UI
	setup_ui()
	
	# 设置初始状态
	update_health_display()
	update_mood_display()
	
	print("Colonist initialized: ", colonist_name)

func _physics_process(delta):
	# 更新需求
	update_needs(delta)
	
	# 处理移动
	handle_movement(delta)
	
	# 更新AI
	update_ai(delta)

func setup_ui():
	if name_label:
		name_label.text = colonist_name
	
	if health_bar:
		health_bar.max_value = max_health
		health_bar.value = current_health
	
	update_mood_display()

func update_needs(delta: float):
	# 饥饿递减
	needs["hunger"] = max(0.0, needs["hunger"] - 5.0 * delta)
	
	# 休息递减（如果不在睡觉）
	if current_task != "sleep":
		needs["rest"] = max(0.0, needs["rest"] - 3.0 * delta)
	else:
		needs["rest"] = min(100.0, needs["rest"] + 20.0 * delta)
	
	# 娱乐递减
	if current_task != "recreation":
		needs["recreation"] = max(0.0, needs["recreation"] - 1.0 * delta)
	else:
		needs["recreation"] = min(100.0, needs["recreation"] + 15.0 * delta)
	
	# 根据需求影响心情
	update_mood_from_needs()

func update_mood_from_needs():
	var mood_modifier = 0.0
	
	# 饥饿影响
	if needs["hunger"] < 20.0:
		mood_modifier -= 20.0
	elif needs["hunger"] < 50.0:
		mood_modifier -= 10.0
	
	# 休息影响
	if needs["rest"] < 20.0:
		mood_modifier -= 15.0
	elif needs["rest"] < 50.0:
		mood_modifier -= 5.0
	
	# 娱乐影响
	if needs["recreation"] < 30.0:
		mood_modifier -= 10.0
	
	# 更新心情
	var new_mood = clamp(base_mood + mood_modifier, 0.0, 100.0)
	if abs(new_mood - current_mood) > 1.0:
		current_mood = new_mood
		mood_changed.emit(current_mood)
		update_mood_display()

func handle_movement(_delta: float):
	if is_moving and target_position != Vector2.ZERO:
		var direction = (target_position - global_position).normalized()
		var distance = global_position.distance_to(target_position)
		
		if distance > 5.0:
			velocity = direction * move_speed
			move_and_slide()
		else:
			# 到达目标
			global_position = target_position
			is_moving = false
			velocity = Vector2.ZERO
			on_reached_target()

func move_to(target_pos: Vector2):
	target_position = target_pos
	is_moving = true

func on_reached_target():
	# 到达目标位置后的处理
	if current_task == "move":
		set_task("idle")

func update_ai(_delta: float):
	# 简单的AI逻辑
	match current_task:
		"idle":
			# 检查是否需要满足基本需求
			if needs["hunger"] < 30.0:
				set_task("eat")
			elif needs["rest"] < 20.0:
				set_task("sleep")
			elif needs["recreation"] < 20.0:
				set_task("recreation")
		
		"eat":
			# 寻找食物
			if needs["hunger"] > 80.0:
				set_task("idle")
		
		"sleep":
			# 睡觉恢复
			if needs["rest"] > 90.0:
				set_task("idle")
		
		"recreation":
			# 娱乐活动
			if needs["recreation"] > 80.0:
				set_task("idle")

func set_task(new_task: String, priority: int = 0):
	if current_task != new_task:
		current_task = new_task
		task_priority = priority
		task_changed.emit(new_task)
		print(colonist_name, " is now: ", new_task)

func take_damage(amount: float, _damage_type: String = "generic"):
	var old_health = current_health
	current_health = max(0.0, current_health - amount)
	
	if current_health != old_health:
		health_changed.emit(current_health)
		update_health_display()
		
		# 添加受伤心情修正
		if GameManager.instance and GameManager.instance.mood_system:
			GameManager.instance.mood_system.add_mood_modifier(
				self, "injured", "Injured", -10.0, 3600.0  # 1小时
			)
	
	if current_health <= 0.0:
		die()

func heal(amount: float):
	var old_health = current_health
	current_health = min(max_health, current_health + amount)
	
	if current_health != old_health:
		health_changed.emit(current_health)
		update_health_display()

func die():
	print(colonist_name, " has died!")
	# 从游戏管理器移除
	if GameManager.instance:
		GameManager.instance.remove_colonist(self)
	
	# 播放死亡动画或效果
	queue_free()

func get_skill_level(skill_name: String) -> int:
	return skills.get(skill_name, 0)

func improve_skill(skill_name: String, amount: int = 1):
	if skill_name in skills:
		var old_level = skills[skill_name]
		skills[skill_name] = min(20, skills[skill_name] + amount)
		
		if skills[skill_name] != old_level:
			skill_improved.emit(skill_name, skills[skill_name])
			print(colonist_name, " improved ", skill_name, " to level ", skills[skill_name])

func add_trait(trait_name: String):
	if trait_name not in traits:
		traits.append(trait_name)
		print(colonist_name, " gained trait: ", trait_name)

func remove_trait(trait_name: String):
	if trait_name in traits:
		traits.erase(trait_name)
		print(colonist_name, " lost trait: ", trait_name)

func has_trait(trait_name: String) -> bool:
	return trait_name in traits

func update_health_display():
	if health_bar:
		health_bar.value = current_health
		
		# 根据健康状态改变颜色
		if current_health < 25.0:
			health_bar.modulate = Color.RED
		elif current_health < 50.0:
			health_bar.modulate = Color.YELLOW
		else:
			health_bar.modulate = Color.GREEN

func update_mood_display():
	if mood_indicator:
		# 根据心情改变颜色
		if current_mood < 25.0:
			mood_indicator.color = Color.RED
		elif current_mood < 50.0:
			mood_indicator.color = Color.ORANGE
		elif current_mood < 75.0:
			mood_indicator.color = Color.YELLOW
		else:
			mood_indicator.color = Color.GREEN

func get_colonist_info() -> Dictionary:
	return {
		"name": colonist_name,
		"age": age,
		"gender": gender,
		"health": current_health,
		"max_health": max_health,
		"mood": current_mood,
		"task": current_task,
		"skills": skills,
		"traits": traits,
		"needs": needs
	}

func save_data() -> Dictionary:
	return {
		"name": colonist_name,
		"age": age,
		"gender": gender,
		"position": global_position,
		"health": current_health,
		"mood": current_mood,
		"skills": skills,
		"traits": traits,
		"needs": needs,
		"current_task": current_task
	}

func load_data(data: Dictionary):
	colonist_name = data.get("name", "Unknown")
	age = data.get("age", 25)
	gender = data.get("gender", "neutral")
	global_position = data.get("position", Vector2.ZERO)
	current_health = data.get("health", 100.0)
	current_mood = data.get("mood", 50.0)
	skills = data.get("skills", {})
	traits = data.get("traits", [])
	needs = data.get("needs", {})
	current_task = data.get("current_task", "idle")
	
	setup_ui()
