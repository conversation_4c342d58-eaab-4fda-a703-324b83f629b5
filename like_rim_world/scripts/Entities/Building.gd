class_name Building
extends Node2D

# 建筑基础类
# 所有建筑物的基类，提供基础功能
# 使用32x32网格对齐系统

signal building_destroyed(building: Building)
signal building_damaged(building: Building, damage: float)
signal building_repaired(building: Building, repair_amount: float)

# 网格系统常量
const GRID_SIZE: int = 32
const HALF_GRID: int = 16

# 建筑类型枚举
enum BuildingType {
	WALL,
	DOOR,
	BED,
	STORAGE,
	KITCHEN,
	WORKBENCH,
	RESEARCH_BENCH,
	POWER_GENERATOR,
	FARM_PLOT
}

# 建筑状态枚举
enum BuildingState {
	UNDER_CONSTRUCTION,
	OPERATIONAL,
	DAMAGED,
	DESTROYED
}

@export var building_name: String = "Building"
@export var building_type: String = "basic"
@export var max_health: float = 100.0
@export var current_health: float = 100.0
@export var construction_cost: Dictionary = {}
@export var power_consumption: float = 0.0
@export var power_production: float = 0.0
@export var is_constructed: bool = false
@export var construction_progress: float = 0.0

var building_id: String = ""
var owner_faction: String = "player"
var construction_materials_needed: Dictionary = {}
var workers_assigned: Array[Node] = []
var is_operational: bool = false

# 网格坐标系统
var grid_position: Vector2i = Vector2i.ZERO
var grid_size: Vector2i = Vector2i.ONE  # 建筑占用的网格大小

func _ready():
	building_id = str(get_instance_id())
	if current_health <= 0:
		current_health = max_health

	# 确保建筑位置对齐到网格
	snap_to_grid()

func _process(delta: float):
	if is_constructed and is_operational:
		update_building_logic(delta)

func update_building_logic(_delta: float):
	# 子类重写此方法实现具体建筑逻辑
	pass

func take_damage(damage: float):
	current_health = max(0, current_health - damage)
	building_damaged.emit(self, damage)
	
	if current_health <= 0:
		destroy_building()

func repair(repair_amount: float):
	var old_health = current_health
	current_health = min(max_health, current_health + repair_amount)
	var actual_repair = current_health - old_health
	
	if actual_repair > 0:
		building_repaired.emit(self, actual_repair)

func destroy_building():
	building_destroyed.emit(self)
	queue_free()

func get_health_percentage() -> float:
	return current_health / max_health if max_health > 0 else 0.0

func is_damaged() -> bool:
	return current_health < max_health

func can_operate() -> bool:
	return is_constructed and current_health > 0

func set_construction_progress(progress: float):
	construction_progress = clamp(progress, 0.0, 1.0)
	if construction_progress >= 1.0:
		complete_construction()

func complete_construction():
	is_constructed = true
	is_operational = true
	construction_progress = 1.0

func get_building_info() -> Dictionary:
	return {
		"id": building_id,
		"name": building_name,
		"type": building_type,
		"health": current_health,
		"max_health": max_health,
		"is_constructed": is_constructed,
		"is_operational": is_operational,
		"power_consumption": power_consumption,
		"power_production": power_production
	}

func assign_worker(worker: Node):
	if worker not in workers_assigned:
		workers_assigned.append(worker)

func remove_worker(worker: Node):
	if worker in workers_assigned:
		workers_assigned.erase(worker)

# 网格对齐系统方法
func snap_to_grid():
	"""将建筑位置对齐到32x32网格"""
	var world_pos = global_position
	grid_position.x = int(round(world_pos.x / GRID_SIZE))
	grid_position.y = int(round(world_pos.y / GRID_SIZE))

	# 更新实际位置到网格中心
	global_position = Vector2(
		grid_position.x * GRID_SIZE + HALF_GRID,
		grid_position.y * GRID_SIZE + HALF_GRID
	)

func set_grid_position(grid_pos: Vector2i):
	"""设置网格坐标并更新世界坐标"""
	grid_position = grid_pos
	global_position = Vector2(
		grid_position.x * GRID_SIZE + HALF_GRID,
		grid_position.y * GRID_SIZE + HALF_GRID
	)

func get_grid_position() -> Vector2i:
	"""获取当前网格坐标"""
	return grid_position

func get_occupied_grid_cells() -> Array[Vector2i]:
	"""获取建筑占用的所有网格单元"""
	var cells: Array[Vector2i] = []
	for x in range(grid_size.x):
		for y in range(grid_size.y):
			cells.append(Vector2i(grid_position.x + x, grid_position.y + y))
	return cells

func is_grid_position_valid(_grid_pos: Vector2i, check_collision: bool = true) -> bool:
	"""检查网格位置是否有效"""
	if not check_collision:
		return true

	# 这里可以添加与其他建筑的碰撞检测
	# 暂时返回true，实际应该检查GameManager中的建筑网格
	return true

# 静态方法：世界坐标转网格坐标
static func world_to_grid(world_pos: Vector2) -> Vector2i:
	"""将世界坐标转换为网格坐标"""
	return Vector2i(
		int(floor(world_pos.x / GRID_SIZE)),
		int(floor(world_pos.y / GRID_SIZE))
	)

# 静态方法：网格坐标转世界坐标
static func grid_to_world(grid_pos: Vector2i) -> Vector2:
	"""将网格坐标转换为世界坐标（网格中心）"""
	return Vector2(
		grid_pos.x * GRID_SIZE + HALF_GRID,
		grid_pos.y * GRID_SIZE + HALF_GRID
	)

func get_worker_count() -> int:
	return workers_assigned.size()
