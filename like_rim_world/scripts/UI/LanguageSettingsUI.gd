extends Control
class_name LanguageSettingsUI

# 语言设置界面

signal language_changed(language: LocalizationManager.Language)
signal settings_closed()

# UI组件
@onready var language_selector: OptionButton = $VBoxContainer/LanguageContainer/LanguageSelector
@onready var preview_panel: RichTextLabel = $VBoxContainer/PreviewPanel
@onready var apply_button: Button = $VBoxContainer/ButtonContainer/ApplyButton
@onready var cancel_button: Button = $VBoxContainer/ButtonContainer/CancelButton
@onready var title_label: Label = $VBoxContainer/TitleLabel

# 当前选择的语言
var selected_language: LocalizationManager.Language
var original_language: LocalizationManager.Language

func _ready():
	setup_ui()
	connect_signals()
	load_current_settings()

func setup_ui():
	# 设置标题
	title_label.text = LocalizationManager.tr_ui("language_settings")
	
	# 设置按钮文本
	apply_button.text = LocalizationManager.get_translation("apply")
	cancel_button.text = LocalizationManager.get_translation("cancel")
	
	# 填充语言选择器
	setup_language_selector()
	
	# 设置预览面板
	update_preview()

func setup_language_selector():
	language_selector.clear()
	
	var available_languages = LocalizationManager.instance.get_available_languages()
	for language in available_languages:
		var language_name = LocalizationManager.instance.get_language_name(language)
		language_selector.add_item(language_name)
		language_selector.set_item_metadata(language_selector.get_item_count() - 1, language)

func connect_signals():
	language_selector.item_selected.connect(_on_language_selected)
	apply_button.pressed.connect(_on_apply_pressed)
	cancel_button.pressed.connect(_on_cancel_pressed)
	
	if LocalizationManager.instance:
		LocalizationManager.instance.language_changed.connect(_on_language_changed)

func load_current_settings():
	if LocalizationManager.instance:
		original_language = LocalizationManager.instance.get_current_language()
		selected_language = original_language
		
		# 在选择器中选择当前语言
		for i in range(language_selector.get_item_count()):
			var language = language_selector.get_item_metadata(i)
			if language == selected_language:
				language_selector.selected = i
				break

func _on_language_selected(index: int):
	selected_language = language_selector.get_item_metadata(index)
	update_preview()

func update_preview():
	if not LocalizationManager.instance:
		return
	
	# 临时切换语言以显示预览
	var current_lang = LocalizationManager.instance.get_current_language()
	LocalizationManager.instance.set_language(selected_language)
	
	# 生成预览文本
	var preview_text = generate_preview_text()
	preview_panel.text = preview_text
	
	# 恢复原语言
	LocalizationManager.instance.set_language(current_lang)

func generate_preview_text() -> String:
	var text = "[b]" + LocalizationManager.tr_ui("preview") + "[/b]\n\n"
	
	# 核心UI文本预览
	text += "[b]" + LocalizationManager.tr_ui("ui_elements") + ":[/b]\n"
	text += "• " + LocalizationManager.get_translation("new_game") + "\n"
	text += "• " + LocalizationManager.get_translation("load_game") + "\n"
	text += "• " + LocalizationManager.get_translation("save_game") + "\n"
	text += "• " + LocalizationManager.get_translation("settings") + "\n\n"
	
	# 游戏数据预览
	text += "[b]" + LocalizationManager.tr_ui("game_content") + ":[/b]\n"
	text += "• " + LocalizationManager.tr_data("faction", "tribal") + "\n"
	text += "• " + LocalizationManager.tr_data("animal", "rabbit") + "\n"
	text += "• " + LocalizationManager.tr_data("weapon", "sword") + "\n"
	text += "• " + LocalizationManager.tr_data("skill", "shooting") + "\n\n"
	
	# 系统信息预览
	text += "[b]" + LocalizationManager.tr_ui("system_info") + ":[/b]\n"
	text += "• " + LocalizationManager.tr_ui("health") + ": 85/100\n"
	text += "• " + LocalizationManager.tr_ui("mood") + ": 72\n"
	text += "• " + LocalizationManager.tr_ui("age") + ": 25\n"
	
	return text

func _on_apply_pressed():
	if LocalizationManager.instance and selected_language != original_language:
		LocalizationManager.instance.set_language(selected_language)
		LocalizationManager.instance.save_language_settings()
		language_changed.emit(selected_language)
		
		# 更新所有UI文本
		refresh_all_ui()
	
	close_settings()

func _on_cancel_pressed():
	# 恢复原始语言选择
	selected_language = original_language
	for i in range(language_selector.get_item_count()):
		var language = language_selector.get_item_metadata(i)
		if language == selected_language:
			language_selector.selected = i
			break
	
	update_preview()
	close_settings()

func _on_language_changed(language_name: String):
	# 语言已更改，更新UI
	setup_ui()

func close_settings():
	visible = false
	settings_closed.emit()

func refresh_all_ui():
	"""刷新游戏中所有UI的文本"""
	# 通知所有UI组件更新文本
	get_tree().call_group("localizable_ui", "update_localization")
	
	# 更新当前界面
	setup_ui()

# 显示语言设置
func show_settings():
	visible = true
	load_current_settings()
	update_preview()

# 添加新的翻译数据到LocalizationManager
func add_ui_translations():
	if not LocalizationManager.instance:
		return
	
	# 为英文添加UI翻译
	var english_ui = LocalizationManager.instance.localization_data["en"]["ui"]
	english_ui.merge({
		"language_settings": "Language Settings",
		"preview": "Preview",
		"ui_elements": "UI Elements",
		"game_content": "Game Content",
		"system_info": "System Information",
		"apply": "Apply",
		"factions": "Factions",
		"vehicles": "Vehicles", 
		"genes": "Genes",
		"ai_units": "AI Units",
		"animals": "Animals",
		"plants": "Plants",
		"weapons": "Weapons",
		"armor": "Armor",
		"food": "Food",
		"drugs": "Drugs",
		"buildings": "Buildings",
		"research": "Research",
		"skills": "Skills",
		"traits": "Traits"
	})
	
	# 为中文添加UI翻译
	var chinese_ui = LocalizationManager.instance.localization_data["zh"]["ui"]
	chinese_ui.merge({
		"language_settings": "语言设置",
		"preview": "预览",
		"ui_elements": "界面元素",
		"game_content": "游戏内容",
		"system_info": "系统信息",
		"apply": "应用",
		"factions": "派系",
		"vehicles": "载具",
		"genes": "基因",
		"ai_units": "AI单元",
		"animals": "动物",
		"plants": "植物",
		"weapons": "武器",
		"armor": "护甲",
		"food": "食物",
		"drugs": "药物",
		"buildings": "建筑",
		"research": "研究",
		"skills": "技能",
		"traits": "特质"
	})

# 本地化组件基类
class LocalizableComponent:
	func update_localization():
		# 子类应该重写这个方法来更新其文本
		pass

# 本地化标签组件
class LocalizableLabel extends Label:
	var localization_key: String = ""
	var localization_category: String = "core"
	
	func set_localization_key(key: String, category: String = "core"):
		localization_key = key
		localization_category = category
		update_localization()
	
	func update_localization():
		if localization_key != "":
			text = LocalizationManager.tr(localization_key, localization_category)

# 本地化按钮组件
class LocalizableButton extends Button:
	var localization_key: String = ""
	var localization_category: String = "core"
	
	func set_localization_key(key: String, category: String = "core"):
		localization_key = key
		localization_category = category
		update_localization()
	
	func update_localization():
		if localization_key != "":
			text = LocalizationManager.tr(localization_key, localization_category)

# 本地化选项按钮组件
class LocalizableOptionButton extends OptionButton:
	var item_keys: Array[String] = []
	var localization_category: String = "core"
	
	func set_localized_items(keys: Array[String], category: String = "core"):
		item_keys = keys
		localization_category = category
		update_localization()
	
	func update_localization():
		clear()
		for key in item_keys:
			var localized_text = LocalizationManager.get_translation(key, localization_category)
			add_item(localized_text)

# 工具函数：创建本地化UI组件
static func create_localized_label(key: String, category: String = "core") -> Label:
	var label = LocalizableLabel.new()
	label.set_localization_key(key, category)
	label.add_to_group("localizable_ui")
	return label

static func create_localized_button(key: String, category: String = "core") -> Button:
	var button = LocalizableButton.new()
	button.set_localization_key(key, category)
	button.add_to_group("localizable_ui")
	return button

static func create_localized_option_button(keys: Array[String], category: String = "core") -> OptionButton:
	var option_button = LocalizableOptionButton.new()
	option_button.set_localized_items(keys, category)
	option_button.add_to_group("localizable_ui")
	return option_button

# 快速本地化现有组件
static func localize_component(component: Control, key: String, category: String = "core"):
	if component is Label:
		(component as Label).text = LocalizationManager.get_translation(key, category)
	elif component is Button:
		(component as Button).text = LocalizationManager.get_translation(key, category)
	elif component is RichTextLabel:
		(component as RichTextLabel).text = LocalizationManager.get_translation(key, category)
	
	# 添加到本地化组
	component.add_to_group("localizable_ui")
