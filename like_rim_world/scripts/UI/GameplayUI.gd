extends Control

# 游戏UI控制器 - 处理游戏界面交互

# UI节点引用
@onready var food_label = $TopPanel/ResourcesContainer/FoodLabel
@onready var wood_label = $TopPanel/ResourcesContainer/WoodLabel
@onready var stone_label = $TopPanel/ResourcesContainer/StoneLabel
@onready var steel_label = $TopPanel/ResourcesContainer/SteelLabel

@onready var pause_button = $TopPanel/TimeControls/PauseButton
@onready var speed1_button = $TopPanel/TimeControls/Speed1Button
@onready var speed2_button = $TopPanel/TimeControls/Speed2Button
@onready var speed3_button = $TopPanel/TimeControls/Speed3Button

@onready var wall_button = $BottomPanel/BuildingButtons/WallButton
@onready var door_button = $BottomPanel/BuildingButtons/DoorButton
@onready var bed_button = $BottomPanel/BuildingButtons/BedButton
@onready var workbench_button = $BottomPanel/BuildingButtons/WorkbenchButton
@onready var cancel_button = $BottomPanel/BuildingButtons/CancelButton

@onready var info_label = $InfoPanel/InfoLabel

# 系统引用
var game_manager
var input_manager

func _ready():
	# 获取系统引用
	game_manager = GameManager

	# 获取InputManager自动加载引用
	input_manager = get_node("/root/InputManager")

	# 连接信号
	call_deferred("connect_signals")

	# 初始化UI
	update_resource_display()
	update_info_display()

	print("GameplayUI initialized")

func connect_signals():
	# 连接游戏管理器信号
	if game_manager:
		game_manager.resource_changed.connect(_on_resource_changed)
	
	# 连接输入管理器信号
	if input_manager:
		input_manager.colonist_selected.connect(_on_colonist_selected)
		input_manager.building_selected.connect(_on_building_selected)
		input_manager.left_click.connect(_on_left_click)
		input_manager.right_click.connect(_on_right_click)
	
	# 连接UI按钮信号
	pause_button.pressed.connect(_on_pause_pressed)
	speed1_button.pressed.connect(func(): _on_speed_pressed(1.0))
	speed2_button.pressed.connect(func(): _on_speed_pressed(2.0))
	speed3_button.pressed.connect(func(): _on_speed_pressed(3.0))
	
	wall_button.pressed.connect(func(): _on_build_button_pressed("wall"))
	door_button.pressed.connect(func(): _on_build_button_pressed("door"))
	bed_button.pressed.connect(func(): _on_build_button_pressed("bed"))
	workbench_button.pressed.connect(func(): _on_build_button_pressed("workbench"))
	cancel_button.pressed.connect(_on_cancel_pressed)

func update_resource_display():
	if game_manager:
		var resources = game_manager.resources
		food_label.text = "Food: " + str(resources.get("food", 0))
		wood_label.text = "Wood: " + str(resources.get("wood", 0))
		stone_label.text = "Stone: " + str(resources.get("stone", 0))
		steel_label.text = "Steel: " + str(resources.get("steel", 0))

func update_info_display():
	var info_text = "[b]Game Controls:[/b]\n"
	info_text += "[color=yellow]WASD[/color] - Move selected colonist\n"
	info_text += "[color=yellow]Left Click[/color] - Select object\n"
	info_text += "[color=yellow]Right Click[/color] - Command move\n"
	info_text += "[color=yellow]Mouse Wheel[/color] - Zoom camera\n"
	info_text += "[color=yellow]Middle Mouse[/color] - Pan camera\n"
	info_text += "[color=yellow]B[/color] - Toggle build mode\n"
	info_text += "[color=yellow]Space[/color] - Pause/Resume\n"
	info_text += "[color=yellow]Escape[/color] - Cancel action\n\n"
	
	info_text += "[b]Selection Info:[/b]\n"
	
	if input_manager:
		var selection_info = input_manager.get_selection_info()
		if selection_info["colonists"] > 0:
			info_text += "Colonists selected: " + str(selection_info["colonists"]) + "\n"
		if selection_info["buildings"] > 0:
			info_text += "Buildings selected: " + str(selection_info["buildings"]) + "\n"
		if selection_info["build_mode"] != "":
			info_text += "[color=green]Build Mode: " + selection_info["build_mode"] + "[/color]\n"
		
		if selection_info["colonists"] == 0 and selection_info["buildings"] == 0 and selection_info["build_mode"] == "":
			info_text += "No objects selected"
	
	info_label.text = info_text

func _process(_delta):
	# 定期更新UI显示
	update_info_display()

# 信号处理函数
func _on_resource_changed(_resource_type: String, _amount: int):
	update_resource_display()

func _on_colonist_selected(colonist):
	print("UI: Colonist selected - ", colonist.colonist_name)
	update_info_display()

func _on_building_selected(building):
	print("UI: Building selected - ", building.building_name)
	update_info_display()

func _on_left_click(click_position: Vector2):
	print("UI: Left click at ", click_position)

func _on_right_click(click_position: Vector2):
	print("UI: Right click at ", click_position)

func _on_pause_pressed():
	if game_manager and game_manager.has_method("pause_game"):
		if game_manager.paused:
			game_manager.resume_game()
			pause_button.text = "Pause"
		else:
			game_manager.pause_game()
			pause_button.text = "Resume"

func _on_speed_pressed(speed: float):
	if game_manager and game_manager.has_method("set_time_speed"):
		game_manager.set_time_speed(speed)

	# 更新按钮状态
	speed1_button.button_pressed = (speed == 1.0)
	speed2_button.button_pressed = (speed == 2.0)
	speed3_button.button_pressed = (speed == 3.0)

func _on_build_button_pressed(building_type: String):
	if input_manager:
		input_manager.set_build_mode(building_type)
	
	# 更新按钮状态
	update_build_button_states(building_type)

func _on_cancel_pressed():
	if input_manager:
		input_manager.cancel_current_action()
	
	# 重置按钮状态
	update_build_button_states("")

func update_build_button_states(active_type: String):
	wall_button.button_pressed = (active_type == "wall")
	door_button.button_pressed = (active_type == "door")
	bed_button.button_pressed = (active_type == "bed")
	workbench_button.button_pressed = (active_type == "workbench")

# 显示建筑信息
func show_building_info(building):
	var info_text = "[b]Building Info:[/b]\n"
	info_text += "Name: " + building.building_name + "\n"
	info_text += "Health: " + str(int(building.current_health)) + "/" + str(int(building.max_health)) + "\n"
	info_text += "State: " + str(building.current_state) + "\n"
	
	if building.current_state == Building.BuildingState.UNDER_CONSTRUCTION:
		var progress = building.get_construction_progress()
		info_text += "Construction: " + str(int(progress)) + "%\n"
	
	info_label.text = info_text

# 显示殖民者信息
func show_colonist_info(colonist):
	var info_text = "[b]Colonist Info:[/b]\n"
	info_text += "Name: " + colonist.colonist_name + "\n"
	info_text += "Health: " + str(int(colonist.current_health)) + "/" + str(int(colonist.max_health)) + "\n"
	info_text += "Mood: " + str(int(colonist.current_mood)) + "\n"
	info_text += "State: " + str(colonist.current_state) + "\n"
	
	info_text += "\n[b]Needs:[/b]\n"
	for need in colonist.needs:
		info_text += need.capitalize() + ": " + str(int(colonist.needs[need])) + "\n"
	
	info_text += "\n[b]Skills:[/b]\n"
	for skill in colonist.skills:
		info_text += skill.capitalize() + ": " + str(colonist.skills[skill]) + "\n"
	
	info_label.text = info_text

# 显示多选信息
func show_multi_selection_info(colonists: Array, buildings: Array):
	var info_text = "[b]Multiple Selection:[/b]\n"
	
	if colonists.size() > 0:
		info_text += "Colonists: " + str(colonists.size()) + "\n"
		for colonist in colonists:
			if is_instance_valid(colonist):
				info_text += "- " + colonist.colonist_name + "\n"
	
	if buildings.size() > 0:
		info_text += "Buildings: " + str(buildings.size()) + "\n"
		for building in buildings:
			if is_instance_valid(building):
				info_text += "- " + building.building_name + "\n"
	
	info_label.text = info_text

# 显示游戏状态
func show_game_status():
	var info_text = "[b]Game Status:[/b]\n"
	
	if game_manager:
		info_text += "Time: " + str(int(game_manager.game_time)) + "s\n"
		info_text += "Speed: " + str(game_manager.time_speed) + "x\n"
		info_text += "Paused: " + str(game_manager.paused) + "\n"
		info_text += "Colonists: " + str(game_manager.colonists.size()) + "\n"
		info_text += "Buildings: " + str(game_manager.buildings.size()) + "\n"
	
	info_label.text = info_text
