extends Control
class_name SimpleUI

# 简化的UI控制器，专门用于测试多语言功能

@onready var title_label: Label = $VBoxContainer/TitleLabel
@onready var status_label: Label = $VBoxContainer/StatusLabel
@onready var content_display: RichTextLabel = $VBoxContainer/ContentDisplay
@onready var english_button: Button = $VBoxContainer/ButtonContainer/EnglishButton
@onready var chinese_button: Button = $VBoxContainer/ButtonContainer/ChineseButton
@onready var test_button: Button = $VBoxContainer/ButtonContainer/TestButton

func _ready():
	print("SimpleUI: 开始初始化")
	setup_ui()
	connect_signals()
	await get_tree().process_frame
	initialize_multilingual()

func setup_ui():
	# 设置初始UI状态
	title_label.text = "RimWorld-like Colony Simulator"
	status_label.text = "正在初始化多语言系统... / Initializing multilingual system..."

	# 添加到本地化组
	add_to_group("localizable_ui")

func connect_signals():
	english_button.pressed.connect(_on_english_pressed)
	chinese_button.pressed.connect(_on_chinese_pressed)
	test_button.pressed.connect(_on_test_pressed)

func initialize_multilingual():
	# LocalizationManager 现在是自动加载单例
	if LocalizationManager:
		print("SimpleUI: LocalizationManager找到")
		status_label.text = "✅ 多语言系统已初始化 / Multilingual system initialized"
		display_welcome_message()
	else:
		print("SimpleUI: LocalizationManager未找到")
		status_label.text = "❌ 多语言系统初始化失败 / Multilingual system failed"

func display_welcome_message():
	var content = "[center][b]多语言功能演示 / Multilingual Feature Demo[/b][/center]\n\n"
	
	content += "[b]当前功能 / Current Features:[/b]\n"
	content += "• 实时语言切换 / Real-time language switching\n"
	content += "• 游戏数据本地化 / Game data localization\n"
	content += "• UI界面翻译 / UI interface translation\n"
	content += "• 设置持久化 / Settings persistence\n\n"
	
	content += "[b]支持的语言 / Supported Languages:[/b]\n"
	if LocalizationManager:
		var available_languages = LocalizationManager.get_available_languages()
		for language in available_languages:
			var lang_name = LocalizationManager.get_language_name(language)
			content += "• " + lang_name + "\n"
	
	content += "\n[b]使用说明 / Instructions:[/b]\n"
	content += "1. 点击语言按钮切换语言 / Click language buttons to switch\n"
	content += "2. 点击测试按钮运行测试 / Click test button to run tests\n"
	content += "3. 观察界面文本变化 / Observe interface text changes\n"
	
	content_display.text = content

func _on_english_pressed():
	print("SimpleUI: 切换到英语")
	if LocalizationManager:
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		update_ui_language()

func _on_chinese_pressed():
	print("SimpleUI: 切换到中文")
	if LocalizationManager:
		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		update_ui_language()

func _on_test_pressed():
	print("SimpleUI: 运行多语言测试")
	run_multilingual_test()

func update_ui_language():
	if not LocalizationManager:
		return

	var current_lang = LocalizationManager.get_current_language()
	var lang_name = LocalizationManager.get_language_name(current_lang)
	
	# 更新标题
	title_label.text = LocalizationManager.get_translation("game_title")

	# 更新状态
	status_label.text = LocalizationManager.tr_ui("current_language") + ": " + lang_name
	
	# 显示示例翻译
	display_sample_translations()

func display_sample_translations():
	var content = "[center][b]" + LocalizationManager.tr_ui("sample_translations") + "[/b][/center]\n\n"
	
	# 基础UI翻译
	content += "[b]" + LocalizationManager.tr_ui("basic_ui") + ":[/b]\n"
	content += "• " + LocalizationManager.get_translation("new_game") + "\n"
	content += "• " + LocalizationManager.get_translation("load_game") + "\n"
	content += "• " + LocalizationManager.get_translation("save_game") + "\n"
	content += "• " + LocalizationManager.get_translation("settings") + "\n\n"
	
	# 游戏数据翻译
	content += "[b]" + LocalizationManager.tr_ui("game_data") + ":[/b]\n"
	content += "• " + LocalizationManager.tr_data("faction", "tribal") + "\n"
	content += "• " + LocalizationManager.tr_data("animal", "wolf") + "\n"
	content += "• " + LocalizationManager.tr_data("weapon", "sword") + "\n"
	content += "• " + LocalizationManager.tr_data("skill", "shooting") + "\n\n"
	
	# UI元素翻译
	content += "[b]" + LocalizationManager.tr_ui("ui_elements") + ":[/b]\n"
	content += "• " + LocalizationManager.tr_ui("health") + "\n"
	content += "• " + LocalizationManager.tr_ui("mood") + "\n"
	content += "• " + LocalizationManager.tr_ui("skills") + "\n"
	content += "• " + LocalizationManager.tr_ui("traits") + "\n"
	
	content_display.text = content

func run_multilingual_test():
	var test_results = "[center][b]" + LocalizationManager.tr_ui("test_results") + "[/b][/center]\n\n"
	
	# 测试1: 基础翻译
	test_results += "[b]" + LocalizationManager.tr_ui("test") + " 1: " + LocalizationManager.tr_ui("basic_translation") + "[/b]\n"
	var basic_test = test_basic_translations()
	test_results += basic_test + "\n\n"
	
	# 测试2: 游戏数据翻译
	test_results += "[b]" + LocalizationManager.tr_ui("test") + " 2: " + LocalizationManager.tr_ui("game_data_translation") + "[/b]\n"
	var data_test = test_game_data_translations()
	test_results += data_test + "\n\n"
	
	# 测试3: 语言切换
	test_results += "[b]" + LocalizationManager.tr_ui("test") + " 3: " + LocalizationManager.tr_ui("language_switching") + "[/b]\n"
	var switch_test = test_language_switching()
	test_results += switch_test + "\n\n"
	
	content_display.text = test_results

func test_basic_translations() -> String:
	var result = ""

	# 测试英文
	LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
	var english_title = LocalizationManager.get_translation("game_title")
	result += "English: " + english_title + "\n"

	# 测试中文
	LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
	var chinese_title = LocalizationManager.get_translation("game_title")
	result += "中文: " + chinese_title + "\n"
	
	if english_title != chinese_title:
		result += "[color=green]✅ " + LocalizationManager.tr_ui("test_passed") + "[/color]"
	else:
		result += "[color=red]❌ " + LocalizationManager.tr_ui("test_failed") + "[/color]"
	
	return result

func test_game_data_translations() -> String:
	var result = ""
	var test_count = 0
	var success_count = 0
	
	var test_data = [
		{"type": "faction", "key": "tribal"},
		{"type": "animal", "key": "wolf"},
		{"type": "weapon", "key": "sword"},
		{"type": "skill", "key": "shooting"}
	]
	
	for data in test_data:
		test_count += 1

		# 英文
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		var english_name = LocalizationManager.tr_data(data["type"], data["key"])

		# 中文
		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		var chinese_name = LocalizationManager.tr_data(data["type"], data["key"])
		
		result += "%s: %s → %s\n" % [data["key"], english_name, chinese_name]
		
		if english_name != chinese_name and english_name != data["key"]:
			success_count += 1
	
	result += "\n" + LocalizationManager.tr_ui("success_rate") + ": %d/%d (%.1f%%)" % [success_count, test_count, success_count * 100.0 / test_count]
	
	if success_count == test_count:
		result += " [color=green]✅[/color]"
	else:
		result += " [color=yellow]⚠️[/color]"
	
	return result

func test_language_switching() -> String:
	var result = ""
	var available_languages = LocalizationManager.get_available_languages()

	result += LocalizationManager.tr_ui("available_languages") + ": %d\n" % available_languages.size()

	for language in available_languages:
		LocalizationManager.set_language(language)
		var lang_name = LocalizationManager.get_language_name(language)
		var game_title = LocalizationManager.get_translation("game_title")
		result += "• %s: %s\n" % [lang_name, game_title]
	
	if available_languages.size() >= 2:
		result += "[color=green]✅ " + LocalizationManager.tr_ui("language_switching_working") + "[/color]"
	else:
		result += "[color=red]❌ " + LocalizationManager.tr_ui("language_switching_failed") + "[/color]"
	
	return result

# 本地化更新方法
func update_localization():
	if LocalizationManager:
		update_ui_language()

# 添加缺失的UI翻译
func add_missing_ui_translations():
	if not LocalizationManager:
		return

	# 英文UI翻译
	var english_ui = LocalizationManager.localization_data["en"]["ui"]
	english_ui.merge({
		"current_language": "Current Language",
		"sample_translations": "Sample Translations",
		"basic_ui": "Basic UI",
		"game_data": "Game Data",
		"ui_elements": "UI Elements",
		"test_results": "Test Results",
		"test": "Test",
		"basic_translation": "Basic Translation",
		"game_data_translation": "Game Data Translation",
		"language_switching": "Language Switching",
		"test_passed": "Test Passed",
		"test_failed": "Test Failed",
		"success_rate": "Success Rate",
		"available_languages": "Available Languages",
		"language_switching_working": "Language switching working",
		"language_switching_failed": "Language switching failed"
	})
	
	# 中文UI翻译
	var chinese_ui = LocalizationManager.localization_data["zh"]["ui"]
	chinese_ui.merge({
		"current_language": "当前语言",
		"sample_translations": "示例翻译",
		"basic_ui": "基础界面",
		"game_data": "游戏数据",
		"ui_elements": "界面元素",
		"test_results": "测试结果",
		"test": "测试",
		"basic_translation": "基础翻译",
		"game_data_translation": "游戏数据翻译",
		"language_switching": "语言切换",
		"test_passed": "测试通过",
		"test_failed": "测试失败",
		"success_rate": "成功率",
		"available_languages": "可用语言",
		"language_switching_working": "语言切换正常",
		"language_switching_failed": "语言切换失败"
	})
