extends Node
class_name SystemManager

# 系统管理器 - 统一管理所有游戏系统

# 系统注册表
var registered_systems: Dictionary = {}
var system_dependencies: Dictionary = {}
var initialization_order: Array[String] = []

# 批量更新器
var batch_updater: DataManager.BatchUpdater

# 系统状态
var systems_initialized: bool = false
var systems_running: bool = false

func _ready():
	batch_updater = DataManager.BatchUpdater.new()
	register_all_systems()
	initialize_systems()

func register_all_systems():
	"""注册所有系统及其依赖关系"""
	var system_configs = {
		"mood": {
			"class": MoodSystem,
			"dependencies": [],
			"update_interval": 1.0
		},
		"medical": {
			"class": MedicalSystem,
			"dependencies": ["mood"],
			"update_interval": 2.0
		},
		"combat": {
			"class": CombatSystem,
			"dependencies": ["medical"],
			"update_interval": 0.1
		},
		"work": {
			"class": WorkSystem,
			"dependencies": ["mood"],
			"update_interval": 5.0
		},
		"room": {
			"class": RoomSystem,
			"dependencies": [],
			"update_interval": 10.0
		},
		"season": {
			"class": SeasonSystem,
			"dependencies": [],
			"update_interval": 1.0
		},
		"storyteller": {
			"class": StorytellerSystem,
			"dependencies": ["mood", "season"],
			"update_interval": 30.0
		},
		"faction": {
			"class": FactionSystem,
			"dependencies": [],
			"update_interval": 60.0
		},
		"vehicle": {
			"class": VehicleSystem,
			"dependencies": [],
			"update_interval": 1.0
		},
		"pollution": {
			"class": PollutionSystem,
			"dependencies": [],
			"update_interval": 10.0
		},
		"religion": {
			"class": ReligionSystem,
			"dependencies": ["mood"],
			"update_interval": 300.0
		},
		"genetic": {
			"class": GeneticSystem,
			"dependencies": ["medical"],
			"update_interval": 1.0
		},
		"ai": {
			"class": AISystem,
			"dependencies": [],
			"update_interval": 0.5
		}
	}
	
	# 注册系统
	for system_name in system_configs:
		register_system(system_name, system_configs[system_name])
	
	# 计算初始化顺序
	initialization_order = calculate_initialization_order()

func register_system(system_name: String, config: Dictionary):
	"""注册单个系统"""
	registered_systems[system_name] = config
	system_dependencies[system_name] = config.get("dependencies", [])

func calculate_initialization_order() -> Array[String]:
	"""计算系统初始化顺序（拓扑排序）"""
	var order: Array[String] = []
	var visited: Dictionary = {}
	var temp_visited: Dictionary = {}

	# 使用辅助方法进行递归访问
	for system_name in registered_systems:
		if system_name not in visited:
			_visit_system_for_order(system_name, order, visited, temp_visited)

	return order

func _visit_system_for_order(system_name: String, order: Array[String], visited: Dictionary, temp_visited: Dictionary):
	"""递归访问系统以确定初始化顺序"""
	if system_name in temp_visited:
		push_error("Circular dependency detected: " + system_name)
		return

	if system_name in visited:
		return

	temp_visited[system_name] = true

	for dependency in system_dependencies.get(system_name, []):
		_visit_system_for_order(dependency, order, visited, temp_visited)

	temp_visited.erase(system_name)
	visited[system_name] = true
	order.append(system_name)

func initialize_systems():
	"""按依赖顺序初始化所有系统"""
	print("Initializing systems in order: ", initialization_order)
	
	for system_name in initialization_order:
		initialize_system(system_name)
	
	setup_batch_updates()
	systems_initialized = true
	systems_running = true
	print("All systems initialized successfully")

func initialize_system(system_name: String):
	"""初始化单个系统"""
	var config = registered_systems.get(system_name, {})
	if config.is_empty():
		push_error("System not found: " + system_name)
		return

	var system_class = config.get("class")
	if not system_class:
		push_error("No class specified for system: " + system_name)
		return

	var system_instance = system_class.new()
	system_instance.name = system_name + "_system"
	add_child(system_instance)

	# 更新配置以包含实例引用
	config["instance"] = system_instance
	registered_systems[system_name] = config

	print("Initialized system: ", system_name)

func setup_batch_updates():
	"""设置批量更新"""
	for system_name in registered_systems:
		var config = registered_systems[system_name]
		var instance = config.get("instance")
		var interval = config.get("update_interval", 1.0)
		
		if instance and instance.has_method("update_system"):
			batch_updater.add_update_function(
				func(delta): instance.update_system(delta),
				interval
			)

func _process(delta: float):
	if systems_running and batch_updater:
		batch_updater.update(delta)

func get_system(system_name: String):
	"""获取系统实例"""
	var config = registered_systems.get(system_name, {})
	return config.get("instance")

func pause_system(system_name: String):
	"""暂停系统"""
	var system = get_system(system_name)
	if system and system.has_method("pause"):
		system.pause()

func resume_system(system_name: String):
	"""恢复系统"""
	var system = get_system(system_name)
	if system and system.has_method("resume"):
		system.resume()

func restart_system(system_name: String):
	"""重启系统"""
	var system = get_system(system_name)
	if system and system.has_method("restart"):
		system.restart()

func get_system_status() -> Dictionary:
	"""获取所有系统状态"""
	var status = {
		"initialized": systems_initialized,
		"running": systems_running,
		"system_count": registered_systems.size(),
		"systems": {}
	}
	
	for system_name in registered_systems:
		var system = get_system(system_name)
		status["systems"][system_name] = {
			"exists": system != null,
			"active": system != null and not system.is_queued_for_deletion()
		}
	
	return status

# 系统间通信
func broadcast_event(event_name: String, data = null):
	"""向所有系统广播事件"""
	for system_name in registered_systems:
		var system = get_system(system_name)
		if system and system.has_method("on_system_event"):
			system.on_system_event(event_name, data)

func send_system_message(from_system: String, to_system: String, message: String, data = null):
	"""系统间发送消息"""
	var target_system = get_system(to_system)
	if target_system and target_system.has_method("on_system_message"):
		target_system.on_system_message(from_system, message, data)

# 性能监控
func get_performance_stats() -> Dictionary:
	"""获取性能统计"""
	var stats = {
		"total_systems": registered_systems.size(),
		"active_systems": 0,
		"memory_usage": {},
		"update_times": {}
	}
	
	for system_name in registered_systems:
		var system = get_system(system_name)
		if system:
			stats["active_systems"] += 1
			
			# 如果系统有性能统计方法
			if system.has_method("get_performance_stats"):
				stats["memory_usage"][system_name] = system.get_performance_stats()

	return stats

# 保存和加载
func save_all_systems() -> Dictionary:
	"""保存所有系统数据"""
	var save_data = {
		"system_manager": {
			"initialized": systems_initialized,
			"running": systems_running
		},
		"systems": {}
	}
	
	for system_name in registered_systems:
		var system = get_system(system_name)
		if system and system.has_method("save_data"):
			save_data["systems"][system_name] = system.save_data()
	
	return save_data

func load_all_systems(save_data: Dictionary):
	"""加载所有系统数据"""
	if "system_manager" in save_data:
		var manager_data = save_data["system_manager"]
		systems_initialized = manager_data.get("initialized", false)
		systems_running = manager_data.get("running", false)
	
	if "systems" in save_data:
		var systems_data = save_data["systems"]
		for system_name in systems_data:
			var system = get_system(system_name)
			if system and system.has_method("load_data"):
				system.load_data(systems_data[system_name])

# 调试和测试
func debug_system(system_name: String) -> Dictionary:
	"""获取系统调试信息"""
	var system = get_system(system_name)
	if not system:
		return {"error": "System not found"}
	
	var debug_info = {
		"name": system_name,
		"class": system.get_script().get_global_name() if system.get_script() else "Unknown",
		"node_path": system.get_path(),
		"children_count": system.get_child_count(),
		"methods": []
	}
	
	# 获取系统方法列表
	var method_list = system.get_method_list()
	for method in method_list:
		if not method["name"].begins_with("_"):
			debug_info["methods"].append(method["name"])
	
	return debug_info

func run_system_test(system_name: String, test_name: String = "basic_test"):
	"""运行系统测试"""
	var system = get_system(system_name)
	if system and system.has_method("run_test"):
		system.run_test(test_name)
	else:
		print("System ", system_name, " does not support testing")

# 热重载支持
func reload_system(system_name: String):
	"""热重载系统"""
	var config = registered_systems.get(system_name, {})
	if config.is_empty():
		return false
	
	var old_system = config.get("instance")
	var save_data = null
	
	# 保存旧系统数据
	if old_system and old_system.has_method("save_data"):
		save_data = old_system.save_data()
	
	# 移除旧系统
	if old_system:
		old_system.queue_free()
	
	# 创建新系统
	initialize_system(system_name)
	
	# 恢复数据
	var new_system = get_system(system_name)
	if new_system and save_data and new_system.has_method("load_data"):
		new_system.load_data(save_data)
	
	print("Reloaded system: ", system_name)
	return true
