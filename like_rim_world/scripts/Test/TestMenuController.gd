extends Control

# 测试菜单控制器

@onready var grid_system_test_btn = $CenterContainer/MainPanel/VBoxContainer/TestButtons/GridSystemTest
@onready var colonist_system_test_btn = $CenterContainer/MainPanel/VBoxContainer/TestButtons/ColonistSystemTest
@onready var game_systems_test_btn = $CenterContainer/MainPanel/VBoxContainer/TestButtons/GameSystemsTest
@onready var all_systems_test_btn = $CenterContainer/MainPanel/VBoxContainer/TestButtons/AllSystemsTest
@onready var back_to_main_btn = $CenterContainer/MainPanel/VBoxContainer/ControlButtons/BackToMain
@onready var exit_game_btn = $CenterContainer/MainPanel/VBoxContainer/ControlButtons/ExitGame

func _ready():
	print("=== 测试菜单初始化 ===")
	connect_signals()
	
	# 设置按钮样式
	setup_button_styles()

func connect_signals():
	"""连接信号"""
	grid_system_test_btn.pressed.connect(_on_grid_system_test_pressed)
	colonist_system_test_btn.pressed.connect(_on_colonist_system_test_pressed)
	game_systems_test_btn.pressed.connect(_on_game_systems_test_pressed)
	all_systems_test_btn.pressed.connect(_on_all_systems_test_pressed)
	back_to_main_btn.pressed.connect(_on_back_to_main_pressed)
	exit_game_btn.pressed.connect(_on_exit_game_pressed)

func setup_button_styles():
	"""设置按钮样式"""
	# 这里可以设置按钮的样式，如果需要的话
	pass

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_ESCAPE:
				_on_back_to_main_pressed()
			KEY_1:
				_on_grid_system_test_pressed()
			KEY_2:
				_on_colonist_system_test_pressed()
			KEY_3:
				_on_game_systems_test_pressed()
			KEY_4:
				_on_all_systems_test_pressed()

# 按钮回调方法
func _on_grid_system_test_pressed():
	"""网格系统测试"""
	print("启动网格系统测试...")
	get_tree().change_scene_to_file("res://scenes/Test/GridSystemTest.tscn")

func _on_colonist_system_test_pressed():
	"""殖民者系统测试"""
	print("启动殖民者系统测试...")
	get_tree().change_scene_to_file("res://scenes/Test/ColonistSystemTest.tscn")

func _on_game_systems_test_pressed():
	"""游戏系统测试"""
	print("启动游戏系统测试...")
	get_tree().change_scene_to_file("res://scenes/Test/GameSystemsTest.tscn")

func _on_all_systems_test_pressed():
	"""全系统综合测试"""
	print("启动全系统综合测试...")
	get_tree().change_scene_to_file("res://scenes/Test/AllSystemsTest.tscn")

func _on_back_to_main_pressed():
	"""返回主菜单"""
	print("返回主菜单...")
	get_tree().change_scene_to_file("res://scenes/Main.tscn")

func _on_exit_game_pressed():
	"""退出游戏"""
	print("退出游戏...")
	get_tree().quit()
