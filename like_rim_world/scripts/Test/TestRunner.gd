extends Node

# 测试运行器 - 自动化测试脚本

class_name TestRunner

# 测试场景路径
const TEST_SCENES = {
	"grid_system": "res://scenes/Test/GridSystemTest.tscn",
	"colonist_system": "res://scenes/Test/ColonistSystemTest.tscn",
	"game_systems": "res://scenes/Test/GameSystemsTest.tscn",
	"all_systems": "res://scenes/Test/AllSystemsTest.tscn",
	"test_menu": "res://scenes/Test/TestMenu.tscn"
}

# 测试结果
var test_results: Dictionary = {}
var current_test: String = ""
var test_start_time: float = 0.0

signal test_completed(test_name: String, success: bool, duration: float)
signal all_tests_completed(results: Dictionary)

func _ready():
	print("TestRunner initialized")

func run_single_test(test_name: String) -> bool:
	"""运行单个测试"""
	if not TEST_SCENES.has(test_name):
		print("Error: Unknown test '%s'" % test_name)
		return false
	
	print("Starting test: %s" % test_name)
	current_test = test_name
	test_start_time = Time.get_time_dict_from_system()["unix"]
	
	# 切换到测试场景
	var scene_path = TEST_SCENES[test_name]
	get_tree().change_scene_to_file(scene_path)
	
	return true

func run_all_tests() -> void:
	"""运行所有测试"""
	print("=== 开始运行所有测试 ===")
	test_results.clear()
	
	var tests = ["grid_system", "colonist_system", "game_systems", "all_systems"]
	
	for test_name in tests:
		await run_test_async(test_name)
		
		# 等待一段时间让测试完成
		await get_tree().create_timer(2.0).timeout
	
	print("=== 所有测试完成 ===")
	print_test_summary()
	all_tests_completed.emit(test_results)

func run_test_async(test_name: String) -> bool:
	"""异步运行测试"""
	print("Running async test: %s" % test_name)
	
	var success = false
	var start_time = Time.get_time_dict_from_system()["unix"]
	
	# GDScript不支持try/except，使用简单的错误处理
	# 这里可以添加具体的测试逻辑
	success = await simulate_test(test_name)
	
	var end_time = Time.get_time_dict_from_system()["unix"]
	var duration = end_time - start_time
	
	test_results[test_name] = {
		"success": success,
		"duration": duration,
		"timestamp": Time.get_datetime_string_from_system()
	}
	
	test_completed.emit(test_name, success, duration)
	return success

func simulate_test(test_name: String) -> bool:
	"""模拟测试执行"""
	print("Simulating test: %s" % test_name)
	
	# 模拟测试时间
	await get_tree().create_timer(randf_range(1.0, 3.0)).timeout
	
	# 模拟测试结果（90%成功率）
	var success = randf() > 0.1
	
	if success:
		print("✓ Test '%s' passed" % test_name)
	else:
		print("✗ Test '%s' failed" % test_name)
	
	return success

func print_test_summary():
	"""打印测试摘要"""
	print("\n=== 测试摘要 ===")
	
	var total_tests = test_results.size()
	var passed_tests = 0
	var total_duration = 0.0
	
	for test_name in test_results.keys():
		var result = test_results[test_name]
		var status = "PASS" if result.success else "FAIL"
		var duration = result.duration
		
		print("%s: %s (%.2fs)" % [test_name, status, duration])
		
		if result.success:
			passed_tests += 1
		total_duration += duration
	
	print("\n总计: %d/%d 测试通过" % [passed_tests, total_tests])
	print("总耗时: %.2f秒" % total_duration)
	print("成功率: %.1f%%" % (float(passed_tests) / float(total_tests) * 100.0))

func get_test_report() -> Dictionary:
	"""获取测试报告"""
	var total_tests = test_results.size()
	var passed_tests = 0
	var total_duration = 0.0
	
	for result in test_results.values():
		if result.success:
			passed_tests += 1
		total_duration += result.duration
	
	return {
		"total_tests": total_tests,
		"passed_tests": passed_tests,
		"failed_tests": total_tests - passed_tests,
		"success_rate": float(passed_tests) / float(total_tests) * 100.0 if total_tests > 0 else 0.0,
		"total_duration": total_duration,
		"results": test_results
	}

# 静态方法，方便从其他地方调用
static func create_and_run_tests() -> TestRunner:
	"""创建并运行测试"""
	var runner = TestRunner.new()
	runner.name = "TestRunner"
	
	# 添加到场景树
	var main = Engine.get_main_loop().current_scene
	if main:
		main.add_child(runner)
	
	return runner

static func quick_test(test_name: String) -> void:
	"""快速运行单个测试"""
	var runner = create_and_run_tests()
	runner.run_single_test(test_name)

static func run_all_tests_static() -> void:
	"""静态方法运行所有测试"""
	var runner = create_and_run_tests()
	runner.run_all_tests()

# 命令行接口
func _notification(what):
	if what == NOTIFICATION_WM_CLOSE_REQUEST:
		print("TestRunner shutting down...")
		get_tree().quit()
