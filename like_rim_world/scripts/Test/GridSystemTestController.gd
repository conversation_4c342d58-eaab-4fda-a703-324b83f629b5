extends Node2D

# 网格系统测试控制器

@onready var camera: Camera2D = $Camera2D
@onready var grid_overlay: Node2D = $GridOverlay
@onready var buildings_node: Node2D = $Buildings
@onready var ui: CanvasLayer = $UI

# 测试用建筑场景
var test_building_scene = preload("res://scripts/Entities/Building.gd")

# 相机控制
var camera_speed: float = 300.0
var zoom_speed: float = 0.1
var min_zoom: float = 0.5
var max_zoom: float = 4.0

# 测试状态
var buildings_placed: Array[Building] = []
var test_game_manager: Node

func _ready():
	print("=== 网格系统测试开始 ===")
	setup_test_environment()
	connect_ui_signals()
	
	# 显示网格
	if grid_overlay.has_method("toggle_grid_display"):
		grid_overlay.show_grid = true
		grid_overlay.queue_redraw()

func setup_test_environment():
	"""设置测试环境"""
	# 创建测试用的GameManager
	test_game_manager = preload("res://scripts/Core/GameManager.gd").new()
	test_game_manager.name = "TestGameManager"
	add_child(test_game_manager)
	
	print("测试环境设置完成")
	print("控制说明:")
	print("- WASD: 移动相机")
	print("- 鼠标滚轮: 缩放")
	print("- G: 切换网格显示")
	print("- H: 切换占用状态显示")
	print("- 鼠标左键: 放置建筑")
	print("- 鼠标右键: 移除建筑")
	print("- ESC: 退出测试")

func connect_ui_signals():
	"""连接UI信号"""
	var grid_toggle = $UI/TestPanel/VBoxContainer/GridToggle
	var occupation_toggle = $UI/TestPanel/VBoxContainer/OccupationToggle
	var place_building = $UI/TestPanel/VBoxContainer/PlaceBuilding
	var remove_building = $UI/TestPanel/VBoxContainer/RemoveBuilding
	var clear_all = $UI/TestPanel/VBoxContainer/ClearAll
	
	grid_toggle.pressed.connect(_on_grid_toggle_pressed)
	occupation_toggle.pressed.connect(_on_occupation_toggle_pressed)
	place_building.pressed.connect(_on_place_building_pressed)
	remove_building.pressed.connect(_on_remove_building_pressed)
	clear_all.pressed.connect(_on_clear_all_pressed)

func _input(event):
	handle_camera_input(event)
	handle_test_input(event)

func handle_camera_input(event):
	"""处理相机输入"""
	# WASD 相机移动
	var camera_movement = Vector2.ZERO
	
	if Input.is_key_pressed(KEY_W):
		camera_movement.y -= 1
	if Input.is_key_pressed(KEY_S):
		camera_movement.y += 1
	if Input.is_key_pressed(KEY_A):
		camera_movement.x -= 1
	if Input.is_key_pressed(KEY_D):
		camera_movement.x += 1
	
	if camera_movement != Vector2.ZERO:
		camera.position += camera_movement.normalized() * camera_speed * get_process_delta_time()
		# 更新网格显示范围
		if grid_overlay.has_method("update_grid_bounds_from_camera"):
			grid_overlay.update_grid_bounds_from_camera(camera)
	
	# 鼠标滚轮缩放
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			var new_zoom = camera.zoom * (1.0 + zoom_speed)
			camera.zoom = Vector2(min(new_zoom.x, max_zoom), min(new_zoom.y, max_zoom))
			if grid_overlay.has_method("update_grid_bounds_from_camera"):
				grid_overlay.update_grid_bounds_from_camera(camera)
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			var new_zoom = camera.zoom * (1.0 - zoom_speed)
			camera.zoom = Vector2(max(new_zoom.x, min_zoom), max(new_zoom.y, min_zoom))
			if grid_overlay.has_method("update_grid_bounds_from_camera"):
				grid_overlay.update_grid_bounds_from_camera(camera)

func handle_test_input(event):
	"""处理测试输入"""
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_G:
				toggle_grid_display()
			KEY_H:
				toggle_occupation_display()
			KEY_ESCAPE:
				exit_test()
	
	if event is InputEventMouseButton and event.pressed:
		match event.button_index:
			MOUSE_BUTTON_LEFT:
				place_building_at_mouse()
			MOUSE_BUTTON_RIGHT:
				remove_building_at_mouse()

func place_building_at_mouse():
	"""在鼠标位置放置建筑"""
	var mouse_pos = get_global_mouse_position()
	var grid_pos = Building.world_to_grid(mouse_pos)
	
	# 检查位置是否可用
	if not test_game_manager.can_place_building_at(grid_pos):
		print("无法在网格位置放置建筑: ", grid_pos, " (已被占用)")
		return
	
	# 创建建筑
	var building = test_building_scene.new()
	building.building_name = "测试建筑 #" + str(buildings_placed.size() + 1)
	building.building_type = "test_wall"
	building.set_grid_position(grid_pos)
	
	# 添加到场景
	buildings_node.add_child(building)
	
	# 注册到测试GameManager
	if test_game_manager.add_building(building):
		buildings_placed.append(building)
		print("建筑已放置在网格: ", grid_pos, " 世界坐标: ", building.global_position)
		
		# 更新网格显示
		if grid_overlay.has_method("queue_redraw"):
			grid_overlay.queue_redraw()
	else:
		building.queue_free()
		print("放置建筑失败")

func remove_building_at_mouse():
	"""移除鼠标位置的建筑"""
	var mouse_pos = get_global_mouse_position()
	var building = test_game_manager.get_building_at_world_pos(mouse_pos)
	
	if building:
		test_game_manager.remove_building(building)
		buildings_placed.erase(building)
		building.queue_free()
		print("建筑已移除")
		
		# 更新网格显示
		if grid_overlay.has_method("queue_redraw"):
			grid_overlay.queue_redraw()
	else:
		print("鼠标位置没有建筑")

func toggle_grid_display():
	"""切换网格显示"""
	if grid_overlay.has_method("toggle_grid_display"):
		grid_overlay.toggle_grid_display()
		print("网格显示: ", grid_overlay.show_grid)

func toggle_occupation_display():
	"""切换占用状态显示"""
	if grid_overlay.has_method("toggle_occupation_display"):
		grid_overlay.toggle_occupation_display()
		print("占用状态显示: ", grid_overlay.show_occupation)

# UI按钮回调
func _on_grid_toggle_pressed():
	toggle_grid_display()

func _on_occupation_toggle_pressed():
	toggle_occupation_display()

func _on_place_building_pressed():
	place_building_at_mouse()

func _on_remove_building_pressed():
	remove_building_at_mouse()

func _on_clear_all_pressed():
	"""清除所有建筑"""
	for building in buildings_placed:
		if is_instance_valid(building):
			test_game_manager.remove_building(building)
			building.queue_free()
	
	buildings_placed.clear()
	print("所有建筑已清除")
	
	# 更新网格显示
	if grid_overlay.has_method("queue_redraw"):
		grid_overlay.queue_redraw()

func exit_test():
	"""退出测试"""
	print("=== 网格系统测试结束 ===")
	print("测试结果:")
	print("- 放置的建筑数量: ", buildings_placed.size())
	print("- 网格系统运行正常: ", test_game_manager != null)
	
	# 返回主场景或退出
	get_tree().change_scene_to_file("res://scenes/Main.tscn")
