extends Node

# 本地化管理器 - 多语言支持系统

signal language_changed(new_language: String)

# 支持的语言
enum Language {
	ENGLISH,
	CHINESE,
	JAPANESE,
	KOREAN,
	SPANISH,
	FRENCH,
	GERMAN,
	RUSSIAN
}

# 当前语言
var current_language: Language = Language.ENGLISH
var fallback_language: Language = Language.ENGLISH

# 本地化数据存储
var localization_data: Dictionary = {}
var loaded_languages: Array[Language] = []

# 单例实例
static var instance: LocalizationManager

func _ready():
	if instance == null:
		instance = self
		initialize_localization()
	else:
		queue_free()

func initialize_localization():
	# 加载所有语言数据
	load_language_data(Language.ENGLISH)
	load_language_data(Language.CHINESE)
	
	# 设置默认语言（根据系统语言）
	detect_system_language()
	
	print("Localization Manager initialized with language: ", get_language_name(current_language))

func detect_system_language():
	var system_locale = OS.get_locale()
	match system_locale.substr(0, 2):
		"zh":
			current_language = Language.CHINESE
		"ja":
			current_language = Language.JAPANESE
		"ko":
			current_language = Language.KOREAN
		"es":
			current_language = Language.SPANISH
		"fr":
			current_language = Language.FRENCH
		"de":
			current_language = Language.GERMAN
		"ru":
			current_language = Language.RUSSIAN
		_:
			current_language = Language.ENGLISH

func load_language_data(language: Language):
	if language in loaded_languages:
		return
	
	var language_key = get_language_key(language)
	localization_data[language_key] = {}
	
	# 加载核心翻译数据
	load_core_translations(language)
	load_game_data_translations(language)
	load_ui_translations(language)
	
	loaded_languages.append(language)

func load_core_translations(language: Language):
	var language_key = get_language_key(language)
	
	match language:
		Language.ENGLISH:
			localization_data[language_key]["core"] = {
				"game_title": "RimWorld-like Colony Simulator",
				"new_game": "New Game",
				"load_game": "Load Game",
				"save_game": "Save Game",
				"settings": "Settings",
				"quit": "Quit",
				"yes": "Yes",
				"no": "No",
				"ok": "OK",
				"cancel": "Cancel",
				"confirm": "Confirm",
				"warning": "Warning",
				"error": "Error",
				"success": "Success"
			}
		
		Language.CHINESE:
			localization_data[language_key]["core"] = {
				"game_title": "类环世界殖民地模拟器",
				"new_game": "新游戏",
				"load_game": "载入游戏",
				"save_game": "保存游戏",
				"settings": "设置",
				"quit": "退出",
				"yes": "是",
				"no": "否",
				"ok": "确定",
				"cancel": "取消",
				"confirm": "确认",
				"warning": "警告",
				"error": "错误",
				"success": "成功"
			}

func load_game_data_translations(language: Language):
	var language_key = get_language_key(language)
	
	match language:
		Language.ENGLISH:
			localization_data[language_key]["game_data"] = {
				# 派系名称
				"faction_tribal": "Tribal Alliance",
				"faction_outlander": "Outlander Union",
				"faction_pirate": "Pirate Confederation",
				"faction_mechanoid": "Mechanoid Hive",
				"faction_spacer": "Spacer Empire",
				"faction_imperial": "Imperial Remnant",
				
				# 载具名称
				"vehicle_ground": "Ground Vehicle",
				"vehicle_aircraft": "Aircraft",
				"vehicle_spacecraft": "Spacecraft",
				"vehicle_mech": "Mech Suit",
				"vehicle_drone": "Drone",
				
				# 动物名称
				"animal_rabbit": "Rabbit",
				"animal_deer": "Deer",
				"animal_wolf": "Wolf",
				"animal_bear": "Bear",
				"animal_chicken": "Chicken",
				"animal_cow": "Cow",
				
				# 植物名称
				"plant_rice": "Rice",
				"plant_potato": "Potato",
				"plant_corn": "Corn",
				"plant_cotton": "Cotton",
				"plant_healroot": "Healroot",
				"plant_devilstrand": "Devilstrand",
				"plant_oak": "Oak Tree",
				"plant_pine": "Pine Tree",
				
				# 武器名称
				"weapon_knife": "Knife",
				"weapon_sword": "Sword",
				"weapon_pistol": "Pistol",
				"weapon_rifle": "Rifle",
				"weapon_sniper": "Sniper Rifle",
				"weapon_minigun": "Minigun",
				
				# 护甲名称
				"armor_tribal": "Tribal Wear",
				"armor_flak": "Flak Vest",
				"armor_power": "Power Armor",
				"armor_helmet": "Helmet",
				
				# 食物名称
				"food_raw_rice": "Raw Rice",
				"food_simple_meal": "Simple Meal",
				"food_fine_meal": "Fine Meal",
				"food_lavish_meal": "Lavish Meal",
				"food_pemmican": "Pemmican",
				"food_survival_meal": "Survival Meal",
				
				# 药物名称
				"drug_medicine": "Medicine",
				"drug_glitterworld": "Glitterworld Medicine",
				"drug_beer": "Beer",
				"drug_smokeleaf": "Smokeleaf Joint",
				"drug_go_juice": "Go-juice",
				"drug_luciferium": "Luciferium",
				
				# 建筑名称
				"building_wall": "Wall",
				"building_door": "Door",
				"building_bed": "Bed",
				"building_research": "Research Bench",
				"building_stove": "Electric Stove",
				"building_hydroponics": "Hydroponics Basin",
				"building_solar": "Solar Generator",
				"building_battery": "Battery",
				
				# 技能名称
				"skill_shooting": "Shooting",
				"skill_melee": "Melee",
				"skill_construction": "Construction",
				"skill_mining": "Mining",
				"skill_cooking": "Cooking",
				"skill_plants": "Plants",
				"skill_animals": "Animals",
				"skill_crafting": "Crafting",
				"skill_artistic": "Artistic",
				"skill_medicine": "Medicine",
				"skill_social": "Social",
				"skill_intellectual": "Intellectual",
				
				# 特质名称
				"trait_brawler": "Brawler",
				"trait_pacifist": "Pacifist",
				"trait_industrious": "Industrious",
				"trait_lazy": "Lazy",
				"trait_beautiful": "Beautiful",
				"trait_ugly": "Ugly",
				"trait_psychopath": "Psychopath",
				"trait_kind": "Kind",
				"trait_night_owl": "Night Owl",
				"trait_cannibal": "Cannibal"
			}
		
		Language.CHINESE:
			localization_data[language_key]["game_data"] = {
				# 派系名称
				"faction_tribal": "部落联盟",
				"faction_outlander": "外来者联邦",
				"faction_pirate": "海盗联盟",
				"faction_mechanoid": "机械体蜂群",
				"faction_spacer": "太空人帝国",
				"faction_imperial": "帝国残余",
				
				# 载具名称
				"vehicle_ground": "地面载具",
				"vehicle_aircraft": "飞行器",
				"vehicle_spacecraft": "太空船",
				"vehicle_mech": "机甲装甲",
				"vehicle_drone": "无人机",
				
				# 动物名称
				"animal_rabbit": "兔子",
				"animal_deer": "鹿",
				"animal_wolf": "狼",
				"animal_bear": "熊",
				"animal_chicken": "鸡",
				"animal_cow": "牛",
				
				# 植物名称
				"plant_rice": "水稻",
				"plant_potato": "土豆",
				"plant_corn": "玉米",
				"plant_cotton": "棉花",
				"plant_healroot": "治疗根",
				"plant_devilstrand": "魔鬼丝",
				"plant_oak": "橡树",
				"plant_pine": "松树",
				
				# 武器名称
				"weapon_knife": "小刀",
				"weapon_sword": "剑",
				"weapon_pistol": "手枪",
				"weapon_rifle": "步枪",
				"weapon_sniper": "狙击步枪",
				"weapon_minigun": "机枪",
				
				# 护甲名称
				"armor_tribal": "部落服装",
				"armor_flak": "防弹背心",
				"armor_power": "动力装甲",
				"armor_helmet": "头盔",
				
				# 食物名称
				"food_raw_rice": "生米",
				"food_simple_meal": "简单餐食",
				"food_fine_meal": "精美餐食",
				"food_lavish_meal": "奢华餐食",
				"food_pemmican": "干肉饼",
				"food_survival_meal": "军用口粮",
				
				# 药物名称
				"drug_medicine": "药物",
				"drug_glitterworld": "闪耀世界药物",
				"drug_beer": "啤酒",
				"drug_smokeleaf": "烟叶卷",
				"drug_go_juice": "兴奋剂",
				"drug_luciferium": "路西法",
				
				# 建筑名称
				"building_wall": "墙壁",
				"building_door": "门",
				"building_bed": "床",
				"building_research": "研究台",
				"building_stove": "电炉",
				"building_hydroponics": "水培盆",
				"building_solar": "太阳能板",
				"building_battery": "电池",
				
				# 技能名称
				"skill_shooting": "射击",
				"skill_melee": "近战",
				"skill_construction": "建造",
				"skill_mining": "采矿",
				"skill_cooking": "烹饪",
				"skill_plants": "种植",
				"skill_animals": "动物",
				"skill_crafting": "制作",
				"skill_artistic": "艺术",
				"skill_medicine": "医疗",
				"skill_social": "社交",
				"skill_intellectual": "智力",
				
				# 特质名称
				"trait_brawler": "好斗者",
				"trait_pacifist": "和平主义者",
				"trait_industrious": "勤劳",
				"trait_lazy": "懒惰",
				"trait_beautiful": "美丽",
				"trait_ugly": "丑陋",
				"trait_psychopath": "精神病患者",
				"trait_kind": "善良",
				"trait_night_owl": "夜猫子",
				"trait_cannibal": "食人者"
			}

func load_ui_translations(language: Language):
	var language_key = get_language_key(language)
	
	match language:
		Language.ENGLISH:
			localization_data[language_key]["ui"] = {
				"colonist_info": "Colonist Information",
				"health": "Health",
				"mood": "Mood",
				"skills": "Skills",
				"traits": "Traits",
				"equipment": "Equipment",
				"age": "Age",
				"level": "Level",
				"passion_minor": "Minor Passion",
				"passion_major": "Major Passion",
				"research": "Research",
				"construction": "Construction",
				"trade": "Trade",
				"diplomacy": "Diplomacy",
				"faction_relations": "Faction Relations",
				"goodwill": "Goodwill",
				"hostile": "Hostile",
				"neutral": "Neutral",
				"friendly": "Friendly",
				"allied": "Allied",
				"data_visualization": "Data Visualization",
				"overview": "Overview",
				"analytics": "Analytics",
				"content_browser": "Content Browser",
				"statistics": "Statistics",
				"export_data": "Export Data",
				"generate_content": "Generate Content",
				"all_types": "All Types",
				"search_placeholder": "Search...",
				"category": "Category",
				"count": "Count",
				"status": "Status",
				"colonists": "Colonists",
				"content": "Content",
				"factions": "Factions",
				"vehicles": "Vehicles",
				"genes": "Genes",
				"ai_units": "AI Units",
				"animals": "Animals",
				"plants": "Plants",
				"weapons": "Weapons",
				"armor": "Armor",
				"food": "Food",
				"drugs": "Drugs",
				"buildings": "Buildings",
			}
		
		Language.CHINESE:
			localization_data[language_key]["ui"] = {
				"colonist_info": "殖民者信息",
				"health": "健康",
				"mood": "心情",
				"skills": "技能",
				"traits": "特质",
				"equipment": "装备",
				"age": "年龄",
				"level": "等级",
				"passion_minor": "小激情",
				"passion_major": "大激情",
				"research": "研究",
				"construction": "建造",
				"trade": "贸易",
				"diplomacy": "外交",
				"faction_relations": "派系关系",
				"goodwill": "善意",
				"hostile": "敌对",
				"neutral": "中立",
				"friendly": "友好",
				"allied": "盟友",
				"data_visualization": "数据可视化",
				"overview": "概览",
				"analytics": "分析",
				"content_browser": "内容浏览器",
				"statistics": "统计",
				"export_data": "导出数据",
				"generate_content": "生成内容",
				"all_types": "所有类型",
				"search_placeholder": "搜索...",
				"category": "类别",
				"count": "数量",
				"status": "状态",
				"colonists": "殖民者",
				"content": "内容",
				"factions": "派系",
				"vehicles": "载具",
				"genes": "基因",
				"ai_units": "AI单元",
				"animals": "动物",
				"plants": "植物",
				"weapons": "武器",
				"armor": "护甲",
				"food": "食物",
				"drugs": "药物",
				"buildings": "建筑",
			}

# 获取翻译文本
func get_text(key: String, category: String = "core") -> String:
	var language_key = get_language_key(current_language)
	
	# 尝试从当前语言获取
	if language_key in localization_data:
		if category in localization_data[language_key]:
			if key in localization_data[language_key][category]:
				return localization_data[language_key][category][key]
	
	# 回退到默认语言
	var fallback_key = get_language_key(fallback_language)
	if fallback_key in localization_data:
		if category in localization_data[fallback_key]:
			if key in localization_data[fallback_key][category]:
				return localization_data[fallback_key][category][key]
	
	# 如果都没找到，返回键名
	return key.replace("_", " ").capitalize()

# 获取游戏数据的本地化名称
func get_localized_name(item_type: String, item_key: String) -> String:
	var localization_key = item_type + "_" + item_key
	return get_text(localization_key, "game_data")

# 获取UI文本
func get_ui_text(key: String) -> String:
	return get_text(key, "ui")

# 语言切换
func set_language(language: Language):
	if language != current_language:
		current_language = language
		if language not in loaded_languages:
			load_language_data(language)
		language_changed.emit(get_language_name(language))
		print("Language changed to: ", get_language_name(language))

func get_current_language() -> Language:
	return current_language

func get_language_name(language: Language) -> String:
	match language:
		Language.ENGLISH: return "English"
		Language.CHINESE: return "中文"
		Language.JAPANESE: return "日本語"
		Language.KOREAN: return "한국어"
		Language.SPANISH: return "Español"
		Language.FRENCH: return "Français"
		Language.GERMAN: return "Deutsch"
		Language.RUSSIAN: return "Русский"
		_: return "Unknown"

func get_language_key(language: Language) -> String:
	match language:
		Language.ENGLISH: return "en"
		Language.CHINESE: return "zh"
		Language.JAPANESE: return "ja"
		Language.KOREAN: return "ko"
		Language.SPANISH: return "es"
		Language.FRENCH: return "fr"
		Language.GERMAN: return "de"
		Language.RUSSIAN: return "ru"
		_: return "en"

func get_available_languages() -> Array[Language]:
	return loaded_languages

# 格式化文本（支持参数替换）
func format_text(key: String, params: Dictionary = {}, category: String = "core") -> String:
	var text = get_text(key, category)
	
	for param_key in params:
		var placeholder = "{" + param_key + "}"
		text = text.replace(placeholder, str(params[param_key]))
	
	return text

# 保存语言设置
func save_language_settings():
	var config = ConfigFile.new()
	config.set_value("localization", "current_language", current_language)
	config.save("user://language_settings.cfg")

# 加载语言设置
func load_language_settings():
	var config = ConfigFile.new()
	if config.load("user://language_settings.cfg") == OK:
		var saved_language = config.get_value("localization", "current_language", Language.ENGLISH)
		set_language(saved_language)

# 静态访问方法
func get_translation(key: String, category: String = "core") -> String:
	return get_text(key, category)

func tr_ui(key: String) -> String:
	return get_ui_text(key)

func tr_data(item_type: String, item_key: String) -> String:
	return get_localized_name(item_type, item_key)
