class_name GeneticSystem
extends Node

# 基因系统
# 管理基因改造和变异
# 功能：
# 1. 注册殖民者到基因系统
# 2. 管理基因数据和变异逻辑

func _ready():
	name = "GeneticSystem"

# 注册殖民者到基因系统
# 参数：
# - colonist: 需要注册的殖民者节点，必须为有效对象
# 返回值：无
func register_colonist(colonist: Node):
	if not colonist or not is_instance_valid(colonist):
		push_error("Invalid colonist provided to GeneticSystem.register_colonist")
		return

	# 初始化殖民者的基因数据
	if not colonist.has_meta("genetic_data"):
		colonist.set_meta("genetic_data", {
			"traits": [],
			"mutations": []
		})

	print("Colonist registered to GeneticSystem: ", colonist.name)