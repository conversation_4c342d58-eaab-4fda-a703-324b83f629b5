class_name TradeSystem
extends Node

# 贸易系统
# 管理与其他派系的贸易

signal trade_offer_received(trader_id: String, offer: Dictionary)
signal trade_completed(trader_id: String, trade_data: Dictionary)

var active_traders: Dictionary = {}
var trade_history: Array[Dictionary] = []

func _ready():
	name = "TradeSystem"

func update_traders(_delta: float):
	# 更新商人状态
	pass

func create_trade_offer(trader_id: String, offered_items: Dictionary, requested_items: Dictionary) -> Dictionary:
	var offer = {
		"trader_id": trader_id,
		"offered_items": offered_items,
		"requested_items": requested_items,
		"timestamp": Time.get_time_dict_from_system()
	}

	# 发出贸易提议信号
	trade_offer_received.emit(trader_id, offer)

	return offer

func accept_trade(offer: Dictionary) -> bool:
	# 简化的贸易逻辑
	trade_completed.emit(offer.trader_id, offer)
	trade_history.append(offer)
	return true

func spawn_random_trader():
	"""生成随机商人并创建贸易提议"""
	var trader_id = "trader_" + str(randi() % 1000)

	# 随机生成商人提供的物品
	var offered_items = {
		"food": randi() % 50 + 10,
		"medicine": randi() % 20 + 5,
		"steel": randi() % 30 + 10
	}

	# 随机生成商人需要的物品
	var requested_items = {
		"wood": randi() % 40 + 15,
		"stone": randi() % 35 + 10
	}

	# 创建贸易提议（这会自动发出 trade_offer_received 信号）
	var offer = create_trade_offer(trader_id, offered_items, requested_items)
	active_traders[trader_id] = offer

	print("Random trader ", trader_id, " has arrived with trade offer!")
	return offer
