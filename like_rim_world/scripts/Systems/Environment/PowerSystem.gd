class_name PowerSystem
extends Node

# 电力系统
# 管理电力生产和消耗

var total_power_production: float = 0.0
var total_power_consumption: float = 0.0
var power_storage: float = 0.0
var max_power_storage: float = 1000.0

func _ready():
	name = "PowerSystem"

func get_power_status() -> Dictionary:
	return {
		"production": total_power_production,
		"consumption": total_power_consumption,
		"storage": power_storage,
		"max_storage": max_power_storage
	}
