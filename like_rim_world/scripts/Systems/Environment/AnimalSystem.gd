extends Node
class_name AnimalSystem

# 动物系统 - 野生动物和驯化系统

signal animal_spawned(animal: SystemAnimal)
signal animal_tamed(animal: SystemAnimal)
@warning_ignore("unused_signal")
signal animal_died(animal: SystemAnimal) # 动物死亡信号，供UI和其他系统监听
signal pack_formed(animals: Array[SystemAnimal])

# 系统内部动物类
class SystemAnimal extends CharacterBody2D:
	var animal_name: String
	var species: String
	var age: float
	var max_health: float
	var current_health: float
	var hunger: float
	var is_tamed: bool = false
	var is_wild: bool = true
	var tameness: float = 0.0
	var training_level: float = 0.0
	var animal_owner: Colonist = null
	
	# 动物属性
	var body_size: float
	var move_speed: float
	var damage: float
	var armor: float
	var wildness: float  # 野性程度 (0-1)
	var manhunter_chance: float
	
	# AI状态
	enum AnimalState {
		GRAZING,
		HUNTING,
		FLEEING,
		ATTACKING,
		FOLLOWING_OWNER,
		SLEEPING,
		MATING
	}
	
	var current_state: AnimalState = AnimalState.GRAZING
	var target: Node2D = null
	var home_position: Vector2
	var pack_members: Array[SystemAnimal] = []
	
	func _init(p_species: String):
		species = p_species
		setup_species_stats()
		animal_name = generate_animal_name()
		age = randf_range(1.0, 10.0)
		current_health = max_health
		hunger = randf_range(30.0, 70.0)
		home_position = global_position
	
	func setup_species_stats():
		match species:
			"deer":
				max_health = 80.0
				body_size = 1.0
				move_speed = 120.0
				damage = 5.0
				armor = 0.0
				wildness = 0.7
				manhunter_chance = 0.01
			"rabbit":
				max_health = 25.0
				body_size = 0.3
				move_speed = 150.0
				damage = 1.0
				armor = 0.0
				wildness = 0.8
				manhunter_chance = 0.0
			"bear":
				max_health = 200.0
				body_size = 2.5
				move_speed = 80.0
				damage = 25.0
				armor = 0.2
				wildness = 0.9
				manhunter_chance = 0.3
			"wolf":
				max_health = 120.0
				body_size = 1.2
				move_speed = 140.0
				damage = 15.0
				armor = 0.1
				wildness = 0.85
				manhunter_chance = 0.15
			"chicken":
				max_health = 30.0
				body_size = 0.4
				move_speed = 60.0
				damage = 2.0
				armor = 0.0
				wildness = 0.3
				manhunter_chance = 0.0
			"cow":
				max_health = 150.0
				body_size = 2.0
				move_speed = 50.0
				damage = 8.0
				armor = 0.1
				wildness = 0.4
				manhunter_chance = 0.02
	
	func generate_animal_name() -> String:
		var names = ["Buddy", "Max", "Luna", "Charlie", "Bella", "Rocky", "Daisy", "Rex"]
		return names[randi() % names.size()]
	
	func update_animal_ai(delta: float):
		update_needs(delta)
		
		match current_state:
			AnimalState.GRAZING:
				if hunger < 30.0:
					find_food()
				elif is_tamed and animal_owner:
					current_state = AnimalState.FOLLOWING_OWNER
			
			AnimalState.HUNTING:
				hunt_behavior(delta)
			
			AnimalState.FLEEING:
				flee_behavior(delta)
			
			AnimalState.ATTACKING:
				attack_behavior(delta)
			
			AnimalState.FOLLOWING_OWNER:
				follow_owner(delta)
	
	func update_needs(delta: float):
		hunger = max(0.0, hunger - 10.0 * delta)
		
		if hunger <= 0.0:
			take_damage(5.0 * delta)  # 饥饿伤害
	
	func find_food():
		# 寻找食物逻辑
		current_state = AnimalState.GRAZING
	
	func hunt_behavior(_delta: float):
		# 狩猎行为 (肉食动物)
		pass

	func flee_behavior(_delta: float):
		# 逃跑行为
		if target:
			var flee_direction = (global_position - target.global_position).normalized()
			velocity = flee_direction * move_speed
			move_and_slide()

	func attack_behavior(_delta: float):
		# 攻击行为
		if target and global_position.distance_to(target.global_position) < 50.0:
			# 攻击目标
			if target.has_method("take_damage"):
				target.take_damage(damage)

	func follow_owner(_delta: float):
		if animal_owner and global_position.distance_to(animal_owner.global_position) > 100.0:
			var direction = (animal_owner.global_position - global_position).normalized()
			velocity = direction * move_speed * 0.8
			move_and_slide()
	
	func attempt_taming(colonist: Colonist) -> bool:
		var taming_skill = colonist.get_skill_level("animals")
		var success_chance = (taming_skill * 0.1) / wildness
		
		if randf() < success_chance:
			tameness += 10.0
			if tameness >= 100.0:
				become_tamed(colonist)
				return true
		else:
			# 失败可能导致动物变得敌对
			if randf() < manhunter_chance:
				become_manhunter()
		
		return false
	
	func become_tamed(tamer: Colonist):
		is_tamed = true
		is_wild = false
		animal_owner = tamer
		tameness = 100.0
		print(animal_name, " has been tamed by ", tamer.colonist_name)
	
	func become_manhunter():
		current_state = AnimalState.ATTACKING
		print(animal_name, " has become a manhunter!")
	
	func take_damage(amount: float):
		current_health = max(0.0, current_health - amount)
		if current_health <= 0.0:
			die()
	
	func die():
		print(animal_name, " has died")
		# 产生肉类和皮革
		var meat_amount = int(body_size * 20)
		var leather_amount = int(body_size * 5)
		GameManager.instance.add_resource("meat", meat_amount)
		GameManager.instance.add_resource("leather", leather_amount)
		queue_free()

# 动物种类数据
var animal_species = {
	"deer": {"spawn_weight": 20, "pack_size": [2, 5], "biome": ["forest", "plains"]},
	"rabbit": {"spawn_weight": 30, "pack_size": [3, 8], "biome": ["forest", "plains"]},
	"bear": {"spawn_weight": 5, "pack_size": [1, 1], "biome": ["forest", "mountain"]},
	"wolf": {"spawn_weight": 10, "pack_size": [2, 6], "biome": ["forest", "mountain"]},
	"chicken": {"spawn_weight": 15, "pack_size": [4, 12], "biome": ["plains"]},
	"cow": {"spawn_weight": 8, "pack_size": [2, 4], "biome": ["plains"]}
}

# 当前地图上的动物
var wild_animals: Array[SystemAnimal] = []
var tamed_animals: Array[SystemAnimal] = []

# 生成计时器
var spawn_timer: Timer

func _ready():
	setup_spawn_timer()
	# 添加肉类和皮革到资源系统
	if GameManager.instance:
		GameManager.instance.resources["meat"] = 0
		GameManager.instance.resources["leather"] = 0
	print("Animal System initialized")

func setup_spawn_timer():
	spawn_timer = Timer.new()
	spawn_timer.wait_time = 30.0  # 每30秒检查一次生成
	spawn_timer.timeout.connect(_on_spawn_timer_timeout)
	spawn_timer.autostart = true
	add_child(spawn_timer)

func _on_spawn_timer_timeout():
	if wild_animals.size() < 20:  # 最大野生动物数量
		attempt_animal_spawn()

func attempt_animal_spawn():
	var total_weight = 0
	for species in animal_species:
		total_weight += animal_species[species]["spawn_weight"]
	
	var random_value = randi() % total_weight
	var current_weight = 0
	
	for species in animal_species:
		current_weight += animal_species[species]["spawn_weight"]
		if random_value < current_weight:
			spawn_animal_pack(species)
			break

func spawn_animal_pack(species: String):
	var pack_data = animal_species[species]
	var pack_size = randi_range(pack_data["pack_size"][0], pack_data["pack_size"][1])
	var spawn_position = get_random_spawn_position()
	
	var pack: Array[SystemAnimal] = []

	for i in range(pack_size):
		var animal = SystemAnimal.new(species)
		animal.global_position = spawn_position + Vector2(randf_range(-50, 50), randf_range(-50, 50))
		
		# 添加到场景
		get_tree().current_scene.add_child(animal)
		wild_animals.append(animal)
		pack.append(animal)
		
		# 设置群体关系
		animal.pack_members = pack
		
		animal_spawned.emit(animal)
	
	if pack.size() > 1:
		pack_formed.emit(pack)
	
	print("Spawned ", pack_size, " ", species, " at ", spawn_position)

func get_random_spawn_position() -> Vector2:
	# 在地图边缘生成动物
	var map_size = Vector2(1000, 1000)  # 假设地图大小
	var side = randi() % 4
	
	match side:
		0:  # 上边
			return Vector2(randf_range(0, map_size.x), -50)
		1:  # 右边
			return Vector2(map_size.x + 50, randf_range(0, map_size.y))
		2:  # 下边
			return Vector2(randf_range(0, map_size.x), map_size.y + 50)
		3:  # 左边
			return Vector2(-50, randf_range(0, map_size.y))
		_:
			return Vector2.ZERO

func tame_animal(animal: Animal, colonist: Colonist) -> bool:
	if animal.is_tamed:
		return false
	
	var success = animal.attempt_taming(colonist)
	if success:
		wild_animals.erase(animal)
		tamed_animals.append(animal)
		animal_tamed.emit(animal)
	
	return success

func hunt_animal(animal: SystemAnimal, hunter: Colonist):
	# 狩猎动物
	animal.current_state = SystemAnimal.AnimalState.FLEEING
	animal.target = hunter
	print(hunter.colonist_name, " is hunting ", animal.animal_name)

func slaughter_animal(animal: SystemAnimal):
	if animal.is_tamed:
		tamed_animals.erase(animal)
	else:
		wild_animals.erase(animal)
	
	animal.die()
	print("Animal slaughtered: ", animal.animal_name)

func get_animals_in_area(center: Vector2, radius: float) -> Array[SystemAnimal]:
	var nearby_animals: Array[SystemAnimal] = []
	
	for animal in wild_animals + tamed_animals:
		if animal.global_position.distance_to(center) <= radius:
			nearby_animals.append(animal)
	
	return nearby_animals

func get_tameable_animals() -> Array[SystemAnimal]:
	var tameable: Array[SystemAnimal] = []
	
	for animal in wild_animals:
		if animal.wildness < 0.9:  # 只有野性不太高的动物可以驯化
			tameable.append(animal)
	
	return tameable

func get_animal_info(animal: SystemAnimal) -> Dictionary:
	return {
		"name": animal.animal_name,
		"species": animal.species,
		"age": animal.age,
		"health": animal.current_health,
		"max_health": animal.max_health,
		"hunger": animal.hunger,
		"is_tamed": animal.is_tamed,
		"tameness": animal.tameness,
		"wildness": animal.wildness,
		"owner": animal.animal_owner.colonist_name if animal.animal_owner else "None",
		"state": SystemAnimal.AnimalState.keys()[animal.current_state]
	}

func feed_animal(animal: SystemAnimal, food_amount: int) -> bool:
	if GameManager.instance.consume_resource("food", food_amount):
		animal.hunger = min(100.0, animal.hunger + food_amount * 10.0)
		print("Fed ", animal.animal_name, " with ", food_amount, " food")
		return true
	return false

func train_animal(animal: SystemAnimal, colonist: Colonist, training_type: String) -> bool:
	if not animal.is_tamed:
		return false
	
	var training_skill = colonist.get_skill_level("animals")
	var success_chance = training_skill * 0.1
	
	if randf() < success_chance:
		animal.training_level += 5.0
		print(animal.animal_name, " learned ", training_type)
		return true
	
	return false

func migrate_animals():
	# 动物迁徙事件
	var migrating_count = randi_range(2, 8)
	var species = animal_species.keys()[randi() % animal_species.size()]
	
	print(migrating_count, " ", species, " are migrating through the area")
	
	# 临时生成迁徙动物
	for i in range(migrating_count):
		var animal = SystemAnimal.new(species)
		animal.global_position = get_random_spawn_position()
		get_tree().current_scene.add_child(animal)
		
		# 设置迁徙行为
		var _exit_position = get_random_spawn_position()
		animal.target = null  # 设置目标为出口位置
		
		# 一段时间后移除
		var timer = Timer.new()
		timer.wait_time = 60.0
		timer.timeout.connect(func(): animal.queue_free())
		timer.autostart = true
		animal.add_child(timer)

func update_animals(delta: float):
	# 更新所有动物
	for animal in wild_animals + tamed_animals:
		if is_instance_valid(animal):
			animal.update_animal_ai(delta)

func save_animal_data() -> Dictionary:
	var save_data = {
		"wild_animals": [],
		"tamed_animals": []
	}
	
	for animal in wild_animals:
		if is_instance_valid(animal):
			save_data["wild_animals"].append({
				"species": animal.species,
				"name": animal.animal_name,
				"position": animal.global_position,
				"health": animal.current_health,
				"hunger": animal.hunger,
				"age": animal.age
			})
	
	for animal in tamed_animals:
		if is_instance_valid(animal):
			save_data["tamed_animals"].append({
				"species": animal.species,
				"name": animal.animal_name,
				"position": animal.global_position,
				"health": animal.current_health,
				"hunger": animal.hunger,
				"age": animal.age,
				"tameness": animal.tameness,
				"training_level": animal.training_level,
				"owner": animal.animal_owner.colonist_name if animal.animal_owner else ""
			})
	
	return save_data

func load_animal_data(save_data: Dictionary):
	# 清除现有动物
	for animal in wild_animals + tamed_animals:
		if is_instance_valid(animal):
			animal.queue_free()
	
	wild_animals.clear()
	tamed_animals.clear()
	
	# 加载野生动物
	if "wild_animals" in save_data:
		for animal_data in save_data["wild_animals"]:
			var animal = SystemAnimal.new(animal_data["species"])
			animal.animal_name = animal_data["name"]
			animal.global_position = animal_data["position"]
			animal.current_health = animal_data["health"]
			animal.hunger = animal_data["hunger"]
			animal.age = animal_data["age"]
			
			get_tree().current_scene.add_child(animal)
			wild_animals.append(animal)
	
	# 加载驯化动物
	if "tamed_animals" in save_data:
		for animal_data in save_data["tamed_animals"]:
			var animal = SystemAnimal.new(animal_data["species"])
			animal.animal_name = animal_data["name"]
			animal.global_position = animal_data["position"]
			animal.current_health = animal_data["health"]
			animal.hunger = animal_data["hunger"]
			animal.age = animal_data["age"]
			animal.tameness = animal_data["tameness"]
			animal.training_level = animal_data["training_level"]
			animal.is_tamed = true
			animal.is_wild = false
			
			# 找到主人
			if animal_data["owner"] != "":
				for colonist in GameManager.instance.colonists:
					if colonist.colonist_name == animal_data["owner"]:
						animal.animal_owner = colonist
						break
			
			get_tree().current_scene.add_child(animal)
			tamed_animals.append(animal)
