class_name ResearchSystem
extends Node

# 研究系统
# 管理科技研究和解锁

signal research_started(research_id: String)
signal research_completed(research_id: String)
signal research_progress_updated(research_id: String, progress: float)

var completed_research: Array[String] = []
var current_research: String = ""
var research_progress: float = 0.0
var research_points: float = 0.0
var research_rate: float = 1.0

func _ready():
	name = "ResearchSystem"

func _process(delta: float):
	if current_research != "":
		update_research_progress(delta)

func start_research(research_id: String) -> bool:
	if not can_research(research_id):
		return false
	
	current_research = research_id
	research_progress = 0.0
	research_started.emit(research_id)
	return true

func can_research(research_id: String) -> bool:
	if research_id in completed_research:
		return false
	
	var config = GameConfig.get_research_config(research_id)
	if config.is_empty():
		return false
	
	# 检查前置研究
	for prerequisite in config.get("prerequisites", []):
		if prerequisite not in completed_research:
			return false
	
	return true

func update_research_progress(delta: float):
	var config = GameConfig.get_research_config(current_research)
	if config.is_empty():
		return
	
	var required_points = config.get("cost", 100)
	var progress_per_second = research_rate * delta
	
	research_progress += progress_per_second
	research_progress_updated.emit(current_research, research_progress / required_points)
	
	if research_progress >= required_points:
		complete_research()

func complete_research():
	completed_research.append(current_research)
	research_completed.emit(current_research)
	
	var config = GameConfig.get_research_config(current_research)
	unlock_research_benefits(config)
	
	current_research = ""
	research_progress = 0.0

func unlock_research_benefits(config: Dictionary):
	var unlocks = config.get("unlocks", [])
	for unlock in unlocks:
		print("Unlocked: ", unlock)

func add_research_points(points: float):
	research_points += points

func get_available_research() -> Array[String]:
	var available: Array[String] = []
	
	for research_id in GameConfig.RESEARCH_CONFIGS.keys():
		if can_research(research_id):
			available.append(research_id)
	
	return available

func get_research_info(research_id: String) -> Dictionary:
	var config = GameConfig.get_research_config(research_id)
	if config.is_empty():
		return {}
	
	return {
		"id": research_id,
		"name": config.get("name", "Unknown"),
		"cost": config.get("cost", 0),
		"prerequisites": config.get("prerequisites", []),
		"unlocks": config.get("unlocks", []),
		"completed": research_id in completed_research,
		"can_research": can_research(research_id)
	}
