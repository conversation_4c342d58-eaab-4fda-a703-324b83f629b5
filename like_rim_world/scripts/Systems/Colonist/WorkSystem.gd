class_name WorkSystem
extends Node

# 工作系统
# 管理殖民者的工作分配和任务执行

signal task_assigned(colonist: Node, task: Dictionary)
signal task_completed(colonist: Node, task: Dictionary)
signal task_failed(colonist: Node, task: Dictionary, reason: String)

var available_tasks: Array[Dictionary] = []
var assigned_tasks: Dictionary = {}
var task_id_counter: int = 0

# 工作类型
enum WorkType {
	MINING,
	CONSTRUCTION,
	FARMING,
	COOKING,
	CLEANING,
	HAULING,
	RESEARCH,
	CRAFTING,
	HUNTING,
	MEDICAL
}

# 任务优先级
enum TaskPriority {
	LOW = 1,
	NORMAL = 2,
	HIGH = 3,
	CRITICAL = 4
}

# 工作优先级（别名）
enum Priority {
	LOW = 1,
	NORMAL = 2,
	HIGH = 3,
	CRITICAL = 4
}

func _ready():
	name = "WorkSystem"

func register_colonist(colonist: Node):
	"""注册殖民者到工作系统"""
	# 确保殖民者加入到工作者组中，便于系统管理
	if not colonist.is_in_group("workers"):
		colonist.add_to_group("workers")

	print("Registered colonist for work system: ", colonist.colonist_name if "colonist_name" in colonist else "Unknown")

func unregister_colonist(colonist: Node):
	"""从工作系统注销殖民者"""
	var colonist_id = str(colonist.get_instance_id())

	# 取消分配给该殖民者的任务
	if assigned_tasks.has(colonist_id):
		var task = assigned_tasks[colonist_id]
		assigned_tasks.erase(colonist_id)
		# 将任务重新加入可用任务列表
		available_tasks.append(task)
		available_tasks.sort_custom(_compare_task_priority)

	# 从工作者组中移除
	if colonist.is_in_group("workers"):
		colonist.remove_from_group("workers")

	print("Unregistered colonist from work system: ", colonist.colonist_name if "colonist_name" in colonist else "Unknown")

func _process(delta: float):
	update_assigned_tasks(delta)
	assign_tasks_to_idle_colonists()

func create_task(work_type: WorkType, position: Vector2, priority: TaskPriority = TaskPriority.NORMAL, requirements: Dictionary = {}) -> int:
	var task_id = task_id_counter
	task_id_counter += 1
	
	var task = {
		"id": task_id,
		"type": work_type,
		"position": position,
		"priority": priority,
		"requirements": requirements,
		"created_time": Time.get_time_dict_from_system(),
		"estimated_duration": get_estimated_duration(work_type),
		"required_skills": get_required_skills(work_type),
		"required_items": requirements.get("items", []),
		"progress": 0.0,
		"max_progress": requirements.get("work_amount", 100.0)
	}
	
	available_tasks.append(task)
	available_tasks.sort_custom(_compare_task_priority)
	
	return task_id

func get_estimated_duration(work_type: WorkType) -> float:
	match work_type:
		WorkType.MINING: return 30.0
		WorkType.CONSTRUCTION: return 60.0
		WorkType.FARMING: return 20.0
		WorkType.COOKING: return 15.0
		WorkType.CLEANING: return 10.0
		WorkType.HAULING: return 5.0
		WorkType.RESEARCH: return 120.0
		WorkType.CRAFTING: return 45.0
		WorkType.HUNTING: return 90.0
		WorkType.MEDICAL: return 40.0
		_: return 30.0

func get_required_skills(work_type: WorkType) -> Array[GameConfig.SkillType]:
	match work_type:
		WorkType.MINING: return [GameConfig.SkillType.MINING]
		WorkType.CONSTRUCTION: return [GameConfig.SkillType.CONSTRUCTION]
		WorkType.FARMING: return [GameConfig.SkillType.GROWING]
		WorkType.COOKING: return [GameConfig.SkillType.COOKING]
		WorkType.CLEANING: return []
		WorkType.HAULING: return []
		WorkType.RESEARCH: return [GameConfig.SkillType.RESEARCH]
		WorkType.CRAFTING: return [GameConfig.SkillType.CRAFTING]
		WorkType.HUNTING: return [GameConfig.SkillType.HUNTING]
		WorkType.MEDICAL: return [GameConfig.SkillType.MEDICAL]
		_: return []

func _compare_task_priority(a: Dictionary, b: Dictionary) -> bool:
	return a.priority > b.priority

func assign_tasks_to_idle_colonists():
	var idle_colonists = get_idle_colonists()
	
	for colonist in idle_colonists:
		var best_task = find_best_task_for_colonist(colonist)
		if best_task:
			assign_task_to_colonist(colonist, best_task)

func get_idle_colonists() -> Array[Node]:
	var idle_colonists: Array[Node] = []
	var all_colonists = get_tree().get_nodes_in_group("colonists")
	
	for colonist in all_colonists:
		var colonist_id = str(colonist.get_instance_id())
		if not assigned_tasks.has(colonist_id):
			if colonist.has_method("is_available_for_work") and colonist.is_available_for_work():
				idle_colonists.append(colonist)
	
	return idle_colonists

func find_best_task_for_colonist(colonist: Node) -> Dictionary:
	var colonist_skills = colonist.get_skills() if colonist.has_method("get_skills") else {}
	var colonist_position = colonist.global_position
	
	var best_task = {}
	var best_score = -1.0
	
	for task in available_tasks:
		var score = calculate_task_suitability(colonist, task, colonist_skills, colonist_position)
		if score > best_score:
			best_score = score
			best_task = task
	
	return best_task if best_score > 0 else {}

func calculate_task_suitability(colonist: Node, task: Dictionary, colonist_skills: Dictionary, colonist_position: Vector2) -> float:
	var score = 0.0
	
	# 优先级权重
	score += task.priority * 10
	
	# 技能匹配
	for required_skill in task.required_skills:
		var skill_level = colonist_skills.get(required_skill, 0)
		score += skill_level * 5
	
	# 距离惩罚
	var distance = colonist_position.distance_to(task.position)
	score -= distance * 0.01
	
	# 检查是否满足要求
	if not meets_task_requirements(colonist, task):
		return 0.0
	
	return score

func meets_task_requirements(colonist: Node, task: Dictionary) -> bool:
	# 检查所需物品
	for item in task.required_items:
		if not colonist.has_method("has_item") or not colonist.has_item(item):
			return false
	
	# 检查最低技能要求
	var colonist_skills = colonist.get_skills() if colonist.has_method("get_skills") else {}
	for required_skill in task.required_skills:
		var min_skill_level = task.requirements.get("min_skill_level", 1)
		if colonist_skills.get(required_skill, 0) < min_skill_level:
			return false
	
	return true

func assign_task_to_colonist(colonist: Node, task: Dictionary):
	var colonist_id = str(colonist.get_instance_id())
	
	# 从可用任务中移除
	available_tasks.erase(task)
	
	# 添加到已分配任务
	task.assigned_colonist = colonist
	task.start_time = Time.get_time_dict_from_system()
	assigned_tasks[colonist_id] = task
	
	# 通知殖民者
	if colonist.has_method("assign_task"):
		colonist.assign_task(task)
	
	task_assigned.emit(colonist, task)

func update_assigned_tasks(delta: float):
	var tasks_to_remove = []
	
	for colonist_id in assigned_tasks.keys():
		var task = assigned_tasks[colonist_id]
		var colonist = task.assigned_colonist
		
		if not is_instance_valid(colonist):
			tasks_to_remove.append(colonist_id)
			continue
		
		# 更新任务进度
		if colonist.has_method("is_working_on_task") and colonist.is_working_on_task():
			var work_speed = get_work_speed(colonist, task)
			task.progress += work_speed * delta
			
			# 检查任务是否完成
			if task.progress >= task.max_progress:
				complete_task(colonist_id, task)
				tasks_to_remove.append(colonist_id)
		
		# 检查任务是否超时或失败
		elif should_fail_task(task):
			fail_task(colonist_id, task, "Task timeout or colonist unavailable")
			tasks_to_remove.append(colonist_id)
	
	# 移除完成或失败的任务
	for colonist_id in tasks_to_remove:
		if assigned_tasks.has(colonist_id):
			assigned_tasks.erase(colonist_id)

func get_work_speed(colonist: Node, task: Dictionary) -> float:
	var base_speed = 1.0
	
	# 技能加成
	if colonist.has_method("get_skills"):
		var skills = colonist.get_skills()
		for required_skill in task.required_skills:
			var skill_level = skills.get(required_skill, 0)
			base_speed += skill_level * 0.1
	
	# 心情影响
	if colonist.has_method("get_mood"):
		var mood = colonist.get_mood()
		base_speed *= (0.5 + mood / 200.0)  # 心情从0-100影响工作效率50%-100%
	
	# 健康影响
	if colonist.has_method("get_health_percentage"):
		var health = colonist.get_health_percentage()
		base_speed *= health
	
	return max(0.1, base_speed)

func complete_task(_colonist_id: String, task: Dictionary):
	var colonist = task.assigned_colonist
	
	# 给予奖励
	give_task_rewards(colonist, task)
	
	# 通知任务完成
	if colonist.has_method("complete_task"):
		colonist.complete_task(task)
	
	task_completed.emit(colonist, task)

func fail_task(_colonist_id: String, task: Dictionary, reason: String):
	var colonist = task.assigned_colonist
	
	# 将任务重新加入可用任务列表
	task.erase("assigned_colonist")
	task.erase("start_time")
	task.progress = 0.0
	available_tasks.append(task)
	available_tasks.sort_custom(_compare_task_priority)
	
	# 通知任务失败
	if colonist and colonist.has_method("fail_task"):
		colonist.fail_task(task)
	
	task_failed.emit(colonist, task, reason)

func should_fail_task(task: Dictionary) -> bool:
	var current_time = Time.get_time_dict_from_system()
	var start_time = task.start_time
	
	# 简化的时间检查（实际应该更复杂）
	var elapsed_hours = (current_time.hour - start_time.hour) + (current_time.day - start_time.day) * 24
	
	# 如果任务超过预估时间的3倍，认为失败
	return elapsed_hours > (task.estimated_duration / 3600.0) * 3

func give_task_rewards(colonist: Node, task: Dictionary):
	# 给予经验值
	if colonist.has_method("gain_skill_experience"):
		for skill in task.required_skills:
			colonist.gain_skill_experience(skill, 10)
	
	# 给予心情奖励
	if colonist.has_method("add_mood_modifier"):
		colonist.add_mood_modifier("completed_work", 2, 3600)  # +2心情，持续1小时

func cancel_task(task_id: int) -> bool:
	# 从可用任务中取消
	for i in range(available_tasks.size()):
		if available_tasks[i].id == task_id:
			available_tasks.remove_at(i)
			return true
	
	# 从已分配任务中取消
	for colonist_id in assigned_tasks.keys():
		var task = assigned_tasks[colonist_id]
		if task.id == task_id:
			var colonist = task.assigned_colonist
			if colonist and colonist.has_method("cancel_task"):
				colonist.cancel_task(task)
			assigned_tasks.erase(colonist_id)
			return true
	
	return false

func get_colonist_current_task(colonist: Node) -> Dictionary:
	var colonist_id = str(colonist.get_instance_id())
	return assigned_tasks.get(colonist_id, {})

func get_work_statistics() -> Dictionary:
	return {
		"available_tasks": available_tasks.size(),
		"assigned_tasks": assigned_tasks.size(),
		"total_tasks_created": task_id_counter
	}
