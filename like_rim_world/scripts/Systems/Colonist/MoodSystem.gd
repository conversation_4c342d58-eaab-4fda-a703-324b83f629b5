extends Node
class_name MoodSystem

# 心情和关系系统 - 复杂的社交互动和心情管理

signal mood_changed(colonist: <PERSON><PERSON>st, new_mood: float)
signal mental_break(colonist: Colonist, break_type: String)
signal relationship_changed(colonist1: Colonist, colonist2: Colonist, new_relationship: float)

# 心情状态
enum MentalState {
	NORMAL,
	STRESSED,
	DEPRESSED,
	BERSERK,
	CATATONIC,
	INSPIRED
}

# 心情修正器类
class MoodModifier:
	var id: String
	var name: String
	var effect: float
	var duration: float = -1.0  # -1 表示永久
	var remaining_time: float
	
	func _init(p_id: String, p_name: String, p_effect: float, p_duration: float = -1.0):
		id = p_id
		name = p_name
		effect = p_effect
		duration = p_duration
		remaining_time = duration
	
	func update(delta: float) -> bool:
		if duration > 0:
			remaining_time -= delta
			return remaining_time > 0
		return true

# 关系类
class Relationship:
	var colonist1: Colonist
	var colonist2: Colonist
	var relationship_value: float = 0.0  # -100 到 100
	var relationship_type: String = "neutral"
	var interactions: Array[Dictionary] = []
	
	func _init(c1: Colonist, c2: Colonist):
		colonist1 = c1
		colonist2 = c2
	
	func add_interaction(interaction_type: String, effect: float):
		interactions.append({
			"type": interaction_type,
			"effect": effect,
			"time": GameManager.instance.game_time
		})
		relationship_value = clamp(relationship_value + effect, -100.0, 100.0)
		update_relationship_type()
	
	func update_relationship_type():
		if relationship_value >= 80:
			relationship_type = "lover"
		elif relationship_value >= 50:
			relationship_type = "friend"
		elif relationship_value >= 20:
			relationship_type = "acquaintance"
		elif relationship_value >= -20:
			relationship_type = "neutral"
		elif relationship_value >= -50:
			relationship_type = "dislike"
		else:
			relationship_type = "enemy"

# 殖民者心情数据
var colonist_moods: Dictionary = {}
var colonist_modifiers: Dictionary = {}
var relationships: Dictionary = {}

func _ready():
	print("Mood System initialized")

func update_mood_system(delta: float):
	update_mood_modifiers(delta)
	update_colonist_moods(delta)
	check_mental_breaks()

func register_colonist(colonist: Colonist):
	if colonist not in colonist_moods:
		colonist_moods[colonist] = {
			"base_mood": 50.0,
			"current_mood": 50.0,
			"mental_state": MentalState.NORMAL,
			"last_break_time": 0.0
		}
		colonist_modifiers[colonist] = []
		print("Registered colonist for mood system: ", colonist.colonist_name)

func unregister_colonist(colonist: Colonist):
	colonist_moods.erase(colonist)
	colonist_modifiers.erase(colonist)
	# 移除所有相关关系
	var to_remove = []
	for key in relationships:
		var rel = relationships[key]
		if rel.colonist1 == colonist or rel.colonist2 == colonist:
			to_remove.append(key)
	for key in to_remove:
		relationships.erase(key)

func add_mood_modifier(colonist: Colonist, modifier_id: String, _modifier_name: String, effect: float, duration: float = -1.0):
	if colonist not in colonist_modifiers:
		register_colonist(colonist)
	
	# 移除同ID的旧修正器
	remove_mood_modifier(colonist, modifier_id)
	
	var modifier = MoodModifier.new(modifier_id, name, effect, duration)
	colonist_modifiers[colonist].append(modifier)
	print("Added mood modifier to ", colonist.colonist_name, ": ", name, " (", effect, ")")

func remove_mood_modifier(colonist: Colonist, modifier_id: String):
	if colonist not in colonist_modifiers:
		return
	
	var modifiers = colonist_modifiers[colonist]
	for i in range(modifiers.size() - 1, -1, -1):
		if modifiers[i].id == modifier_id:
			modifiers.remove_at(i)
			break

func update_mood_modifiers(delta: float):
	for colonist in colonist_modifiers:
		var modifiers = colonist_modifiers[colonist]
		for i in range(modifiers.size() - 1, -1, -1):
			if not modifiers[i].update(delta):
				modifiers.remove_at(i)

func update_colonist_moods(_delta: float):
	for colonist in colonist_moods:
		var mood_data = colonist_moods[colonist]
		var total_modifier = 0.0
		
		# 计算所有修正器的总效果
		if colonist in colonist_modifiers:
			for modifier in colonist_modifiers[colonist]:
				total_modifier += modifier.effect
		
		# 更新当前心情
		var old_mood = mood_data["current_mood"]
		mood_data["current_mood"] = clamp(mood_data["base_mood"] + total_modifier, 0.0, 100.0)
		
		# 如果心情发生显著变化，发出信号
		if abs(mood_data["current_mood"] - old_mood) > 1.0:
			mood_changed.emit(colonist, mood_data["current_mood"])

func check_mental_breaks():
	for colonist in colonist_moods:
		var mood_data = colonist_moods[colonist]
		var current_mood = mood_data["current_mood"]
		var current_state = mood_data["mental_state"]
		
		# 检查精神崩溃条件
		if current_mood < 10.0 and current_state == MentalState.NORMAL:
			if randf() < 0.01:  # 1% 概率每次检查
				trigger_mental_break(colonist)
		elif current_mood > 80.0 and current_state == MentalState.NORMAL:
			if randf() < 0.005:  # 0.5% 概率获得灵感
				trigger_inspiration(colonist)

func trigger_mental_break(colonist: Colonist):
	var break_types = ["berserk", "catatonic", "depression"]
	var break_type = break_types[randi() % break_types.size()]
	
	colonist_moods[colonist]["mental_state"] = MentalState.BERSERK if break_type == "berserk" else MentalState.CATATONIC
	colonist_moods[colonist]["last_break_time"] = GameManager.instance.game_time
	
	mental_break.emit(colonist, break_type)
	print(colonist.colonist_name, " has a mental break: ", break_type)

func trigger_inspiration(colonist: Colonist):
	colonist_moods[colonist]["mental_state"] = MentalState.INSPIRED
	add_mood_modifier(colonist, "inspired", "Inspired", 20.0, 86400.0)  # 1天
	print(colonist.colonist_name, " is inspired!")

func get_mood_info(colonist: Colonist) -> Dictionary:
	if colonist not in colonist_moods:
		return {}
	
	var mood_data = colonist_moods[colonist]
	var modifiers_info = []
	
	if colonist in colonist_modifiers:
		for modifier in colonist_modifiers[colonist]:
			modifiers_info.append({
				"id": modifier.id,
				"name": modifier.name,
				"effect": modifier.effect,
				"duration": modifier.duration,
				"remaining": modifier.remaining_time
			})
	
	return {
		"base_mood": mood_data["base_mood"],
		"current_mood": mood_data["current_mood"],
		"mental_state": MentalState.keys()[mood_data["mental_state"]],
		"modifiers": modifiers_info
	}

func add_relationship_interaction(colonist1: Colonist, colonist2: Colonist, interaction_type: String, effect: float):
	var key = get_relationship_key(colonist1, colonist2)
	
	if key not in relationships:
		relationships[key] = Relationship.new(colonist1, colonist2)
	
	relationships[key].add_interaction(interaction_type, effect)
	relationship_changed.emit(colonist1, colonist2, relationships[key].relationship_value)

func get_relationship_key(colonist1: Colonist, colonist2: Colonist) -> String:
	var id1 = colonist1.get_instance_id()
	var id2 = colonist2.get_instance_id()
	if id1 < id2:
		return str(id1) + "_" + str(id2)
	else:
		return str(id2) + "_" + str(id1)

func get_relationship(colonist1: Colonist, colonist2: Colonist) -> Relationship:
	var key = get_relationship_key(colonist1, colonist2)
	return relationships.get(key)

func save_data() -> Dictionary:
	return {
		"colonist_moods": colonist_moods,
		"relationships": relationships
	}

func load_data(data: Dictionary):
	colonist_moods = data.get("colonist_moods", {})
	relationships = data.get("relationships", {})
