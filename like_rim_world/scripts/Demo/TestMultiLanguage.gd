extends Node
class_name TestMultiLanguage

# 多语言功能测试脚本

func _ready():
	print("=== 多语言功能测试开始 ===")
	test_localization_system()

func test_localization_system():
	# 等待LocalizationManager初始化
	await get_tree().process_frame

	if not LocalizationManager:
		print("错误：LocalizationManager未初始化")
		return

	print("✓ LocalizationManager已初始化")
	
	# 测试基础翻译
	test_basic_translations()
	
	# 测试游戏数据翻译
	test_game_data_translations()
	
	# 测试语言切换
	test_language_switching()
	
	# 测试翻译完整性
	test_translation_completeness()
	
	print("=== 多语言功能测试完成 ===")

func test_basic_translations():
	print("\n--- 测试基础翻译 ---")

	# 测试英文
	LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
	var english_title = LocalizationManager.get_translation("game_title")
	print("英文标题: ", english_title)

	# 测试中文
	LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
	var chinese_title = LocalizationManager.get_translation("game_title")
	print("中文标题: ", chinese_title)

	# 测试UI文本
	var health_en = LocalizationManager.tr_ui("health")
	print("健康(中文): ", health_en)

	LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
	var health_zh = LocalizationManager.tr_ui("health")
	print("Health(英文): ", health_zh)

func test_game_data_translations():
	print("\n--- 测试游戏数据翻译 ---")
	
	var test_data = [
		{"type": "faction", "key": "tribal"},
		{"type": "faction", "key": "pirate"},
		{"type": "animal", "key": "wolf"},
		{"type": "animal", "key": "rabbit"},
		{"type": "weapon", "key": "sword"},
		{"type": "weapon", "key": "pistol"},
		{"type": "skill", "key": "shooting"},
		{"type": "skill", "key": "cooking"},
		{"type": "trait", "key": "beautiful"},
		{"type": "trait", "key": "lazy"}
	]
	
	for data in test_data:
		# 英文
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		var english_name = LocalizationManager.tr_data(data["type"], data["key"])

		# 中文
		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		var chinese_name = LocalizationManager.tr_data(data["type"], data["key"])

		print("%s.%s: %s → %s" % [data["type"], data["key"], english_name, chinese_name])

func test_language_switching():
	print("\n--- 测试语言切换 ---")

	var available_languages = LocalizationManager.get_available_languages()
	print("可用语言: ", available_languages.size(), "种")

	for language in available_languages:
		LocalizationManager.set_language(language)
		var lang_name = LocalizationManager.get_language_name(language)
		var game_title = LocalizationManager.get_translation("game_title")
		print("语言: %s, 游戏标题: %s" % [lang_name, game_title])

func test_translation_completeness():
	print("\n--- 测试翻译完整性 ---")
	
	var test_keys = [
		{"type": "faction", "keys": ["tribal", "outlander", "pirate"]},
		{"type": "animal", "keys": ["rabbit", "deer", "wolf"]},
		{"type": "weapon", "keys": ["knife", "sword", "pistol"]},
		{"type": "skill", "keys": ["shooting", "melee", "construction"]},
		{"type": "trait", "keys": ["brawler", "pacifist", "industrious"]}
	]
	
	var missing_english = []
	var missing_chinese = []
	var total_tested = 0
	
	for test_group in test_keys:
		for key in test_group["keys"]:
			total_tested += 1

			# 测试英文
			LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
			var english_text = LocalizationManager.tr_data(test_group["type"], key)
			if english_text == key:  # 如果返回原键名，说明翻译缺失
				missing_english.append(test_group["type"] + "." + key)

			# 测试中文
			LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
			var chinese_text = LocalizationManager.tr_data(test_group["type"], key)
			if chinese_text == key:  # 如果返回原键名，说明翻译缺失
				missing_chinese.append(test_group["type"] + "." + key)
	
	print("总测试条目: ", total_tested)
	print("缺失英文翻译: ", missing_english.size(), "个")
	if missing_english.size() > 0:
		print("  ", missing_english)
	
	print("缺失中文翻译: ", missing_chinese.size(), "个")
	if missing_chinese.size() > 0:
		print("  ", missing_chinese)
	
	var completeness_en = (total_tested - missing_english.size()) * 100.0 / total_tested
	var completeness_zh = (total_tested - missing_chinese.size()) * 100.0 / total_tested
	
	print("英文翻译完整度: %.1f%%" % completeness_en)
	print("中文翻译完整度: %.1f%%" % completeness_zh)

func test_gameconfig_integration():
	print("\n--- 测试GameConfig集成 ---")
	
	# 测试本地化配置获取
	var faction_config = GameConfig.get_localized_config("faction", "tribal")
	print("部落派系本地化配置:")
	print("  display_name: ", faction_config.get("display_name", "未找到"))
	print("  description: ", faction_config.get("description", "未找到"))
	
	# 测试所有类型的本地化列表
	var all_types = GameConfig.get_all_localized_types()
	print("所有本地化类型:")
	for type_key in all_types:
		print("  %s: %s" % [type_key, all_types[type_key]])

func demonstrate_multilingual_display():
	print("\n--- 多语言显示演示 ---")
	
	var demo_items = [
		{"type": "faction", "key": "tribal", "name": "部落联盟"},
		{"type": "animal", "key": "wolf", "name": "狼"},
		{"type": "weapon", "key": "sword", "name": "剑"},
		{"type": "skill", "key": "shooting", "name": "射击"}
	]
	
	print("| 类型 | 键名 | 英文 | 中文 |")
	print("|------|------|------|------|")
	
	for item in demo_items:
		# 获取英文名称
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		var english_name = LocalizationManager.tr_data(item["type"], item["key"])

		# 获取中文名称
		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		var chinese_name = LocalizationManager.tr_data(item["type"], item["key"])

		print("| %s | %s | %s | %s |" % [item["type"], item["key"], english_name, chinese_name])

func export_translation_report():
	print("\n--- 导出翻译报告 ---")
	
	var report = {
		"export_time": Time.get_datetime_string_from_system(),
		"available_languages": [],
		"translation_statistics": {},
		"sample_translations": {}
	}
	
	# 收集可用语言
	for language in LocalizationManager.get_available_languages():
		var lang_info = {
			"code": LocalizationManager.get_language_key(language),
			"name": LocalizationManager.get_language_name(language)
		}
		report["available_languages"].append(lang_info)

	# 收集翻译统计
	var categories = ["core", "game_data", "ui"]
	for category in categories:
		report["translation_statistics"][category] = {}

		for language in LocalizationManager.get_available_languages():
			var lang_key = LocalizationManager.get_language_key(language)
			var lang_data = LocalizationManager.localization_data.get(lang_key, {})
			var category_data = lang_data.get(category, {})
			report["translation_statistics"][category][lang_key] = category_data.size()
	
	# 收集示例翻译
	var sample_keys = ["game_title", "new_game", "health", "faction_tribal", "animal_wolf"]
	for key in sample_keys:
		report["sample_translations"][key] = {}

		for language in LocalizationManager.get_available_languages():
			LocalizationManager.set_language(language)
			var lang_key = LocalizationManager.get_language_key(language)
			
			var translation = ""
			if key.begins_with("faction_") or key.begins_with("animal_"):
				var parts = key.split("_")
				translation = LocalizationManager.tr_data(parts[0], parts[1])
			elif key in ["health"]:
				translation = LocalizationManager.tr_ui(key)
			else:
				translation = LocalizationManager.get_translation(key)
			
			report["sample_translations"][key][lang_key] = translation
	
	# 保存报告
	var file_path = "user://translation_report.json"
	if Utils.save_json(report, file_path):
		print("翻译报告已导出到: ", file_path)
	else:
		print("导出翻译报告失败")
	
	return report

# 运行完整测试套件
func run_full_test_suite():
	print("=== 运行完整多语言测试套件 ===")
	
	test_localization_system()
	test_gameconfig_integration()
	demonstrate_multilingual_display()
	export_translation_report()
	
	print("=== 测试套件完成 ===")

# 快速验证多语言功能
func quick_validation():
	if not LocalizationManager:
		print("❌ LocalizationManager未初始化")
		return false

	# 测试基础功能
	LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
	var en_title = LocalizationManager.get_translation("game_title")

	LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
	var zh_title = LocalizationManager.get_translation("game_title")
	
	if en_title != zh_title and en_title != "game_title" and zh_title != "game_title":
		print("✅ 多语言功能正常工作")
		print("  英文: ", en_title)
		print("  中文: ", zh_title)
		return true
	else:
		print("❌ 多语言功能异常")
		return false
