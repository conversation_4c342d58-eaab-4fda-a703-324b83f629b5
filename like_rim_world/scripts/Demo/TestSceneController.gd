extends Control
class_name TestSceneController

# 测试场景控制器

@onready var title_label: Label = $VBoxContainer/TitleLabel
@onready var status_label: Label = $VBoxContainer/StatusLabel
@onready var content_display: RichTextLabel = $VBoxContainer/ContentDisplay
@onready var english_button: Button = $VBoxContainer/ButtonContainer/EnglishButton
@onready var chinese_button: Button = $VBoxContainer/ButtonContainer/ChineseButton
@onready var test_button: Button = $VBoxContainer/ButtonContainer/TestButton

var test_runner: TestMultiLanguage

func _ready():
	setup_test_environment()
	connect_signals()
	run_initial_test()

func setup_test_environment():
	# LocalizationManager 现在是自动加载单例，直接访问
	test_runner = $TestMultiLanguage

	# 等待初始化完成
	await get_tree().process_frame

	if LocalizationManager:
		status_label.text = "✅ LocalizationManager已初始化 / LocalizationManager Initialized"
	else:
		status_label.text = "❌ LocalizationManager初始化失败 / LocalizationManager Failed"

func connect_signals():
	english_button.pressed.connect(_on_english_pressed)
	chinese_button.pressed.connect(_on_chinese_pressed)
	test_button.pressed.connect(_on_test_pressed)

func _on_english_pressed():
	if LocalizationManager:
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		update_ui_language()

func _on_chinese_pressed():
	if LocalizationManager:
		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		update_ui_language()

func _on_test_pressed():
	run_comprehensive_test()

func update_ui_language():
	if not LocalizationManager:
		return

	var current_lang = LocalizationManager.get_current_language()
	var lang_name = LocalizationManager.get_language_name(current_lang)
	
	# 更新状态标签
	status_label.text = "当前语言 / Current Language: " + lang_name
	
	# 显示一些示例翻译
	display_sample_translations()

func display_sample_translations():
	var content = "[center][b]示例翻译 / Sample Translations[/b][/center]\n\n"
	
	# 基础UI翻译
	content += "[b]基础UI / Basic UI:[/b]\n"
	content += "• " + LocalizationManager.get_translation("game_title") + "\n"
	content += "• " + LocalizationManager.get_translation("new_game") + "\n"
	content += "• " + LocalizationManager.get_translation("settings") + "\n\n"
	
	# 游戏数据翻译
	content += "[b]游戏数据 / Game Data:[/b]\n"
	content += "• " + LocalizationManager.tr_data("faction", "tribal") + "\n"
	content += "• " + LocalizationManager.tr_data("animal", "wolf") + "\n"
	content += "• " + LocalizationManager.tr_data("weapon", "sword") + "\n"
	content += "• " + LocalizationManager.tr_data("skill", "shooting") + "\n\n"
	
	# UI元素翻译
	content += "[b]UI元素 / UI Elements:[/b]\n"
	content += "• " + LocalizationManager.tr_ui("health") + "\n"
	content += "• " + LocalizationManager.tr_ui("mood") + "\n"
	content += "• " + LocalizationManager.tr_ui("skills") + "\n"
	
	content_display.text = content

func run_initial_test():
	await get_tree().process_frame

	if LocalizationManager:
		# 快速验证
		var validation_result = quick_validation()
		if validation_result:
			display_sample_translations()
		else:
			content_display.text = "[color=red]多语言功能验证失败 / Multilingual validation failed[/color]"
	else:
		content_display.text = "[color=red]LocalizationManager未找到 / LocalizationManager not found[/color]"

func quick_validation() -> bool:
	if not LocalizationManager:
		return false

	# 测试基础功能
	LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
	var en_title = LocalizationManager.get_translation("game_title")

	LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
	var zh_title = LocalizationManager.get_translation("game_title")
	
	return en_title != zh_title and en_title != "game_title" and zh_title != "game_title"

func run_comprehensive_test():
	content_display.text = "[center][b]运行综合测试... / Running Comprehensive Test...[/b][/center]\n\n"
	
	await get_tree().process_frame
	
	var test_results = ""
	
	# 测试1: 基础翻译
	test_results += "[b]测试1: 基础翻译 / Test 1: Basic Translation[/b]\n"
	var basic_test = test_basic_translations()
	test_results += basic_test + "\n\n"
	
	# 测试2: 游戏数据翻译
	test_results += "[b]测试2: 游戏数据翻译 / Test 2: Game Data Translation[/b]\n"
	var data_test = test_game_data_translations()
	test_results += data_test + "\n\n"
	
	# 测试3: 语言切换
	test_results += "[b]测试3: 语言切换 / Test 3: Language Switching[/b]\n"
	var switch_test = test_language_switching()
	test_results += switch_test + "\n\n"
	
	# 测试4: 翻译完整性
	test_results += "[b]测试4: 翻译完整性 / Test 4: Translation Completeness[/b]\n"
	var completeness_test = test_translation_completeness()
	test_results += completeness_test + "\n\n"
	
	content_display.text = test_results

func test_basic_translations() -> String:
	var result = ""

	# 测试英文
	LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
	var english_title = LocalizationManager.get_translation("game_title")
	result += "英文标题 / English Title: " + english_title + "\n"

	# 测试中文
	LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
	var chinese_title = LocalizationManager.get_translation("game_title")
	result += "中文标题 / Chinese Title: " + chinese_title + "\n"
	
	if english_title != chinese_title:
		result += "[color=green]✅ 基础翻译正常 / Basic translation working[/color]"
	else:
		result += "[color=red]❌ 基础翻译异常 / Basic translation failed[/color]"
	
	return result

func test_game_data_translations() -> String:
	var result = ""
	var test_count = 0
	var success_count = 0
	
	var test_data = [
		{"type": "faction", "key": "tribal"},
		{"type": "animal", "key": "wolf"},
		{"type": "weapon", "key": "sword"},
		{"type": "skill", "key": "shooting"}
	]
	
	for data in test_data:
		test_count += 1

		# 英文
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		var english_name = LocalizationManager.tr_data(data["type"], data["key"])

		# 中文
		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		var chinese_name = LocalizationManager.tr_data(data["type"], data["key"])
		
		result += "%s: %s → %s\n" % [data["key"], english_name, chinese_name]
		
		if english_name != chinese_name and english_name != data["key"]:
			success_count += 1
	
	result += "\n成功率 / Success Rate: %d/%d (%.1f%%)" % [success_count, test_count, success_count * 100.0 / test_count]
	
	if success_count == test_count:
		result += " [color=green]✅[/color]"
	else:
		result += " [color=yellow]⚠️[/color]"
	
	return result

func test_language_switching() -> String:
	var result = ""
	var available_languages = LocalizationManager.get_available_languages()

	result += "可用语言 / Available Languages: %d\n" % available_languages.size()

	for language in available_languages:
		LocalizationManager.set_language(language)
		var lang_name = LocalizationManager.get_language_name(language)
		var game_title = LocalizationManager.get_translation("game_title")
		result += "• %s: %s\n" % [lang_name, game_title]
	
	if available_languages.size() >= 2:
		result += "[color=green]✅ 语言切换正常 / Language switching working[/color]"
	else:
		result += "[color=red]❌ 语言切换异常 / Language switching failed[/color]"
	
	return result

func test_translation_completeness() -> String:
	var result = ""
	var test_keys = [
		{"type": "faction", "keys": ["tribal", "pirate"]},
		{"type": "animal", "keys": ["wolf", "rabbit"]},
		{"type": "weapon", "keys": ["sword", "pistol"]},
		{"type": "skill", "keys": ["shooting", "cooking"]}
	]
	
	var missing_english = []
	var missing_chinese = []
	var total_tested = 0
	
	for test_group in test_keys:
		for key in test_group["keys"]:
			total_tested += 1
			
			# 测试英文
			LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
			var english_text = LocalizationManager.tr_data(test_group["type"], key)
			if english_text == key:
				missing_english.append(test_group["type"] + "." + key)

			# 测试中文
			LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
			var chinese_text = LocalizationManager.tr_data(test_group["type"], key)
			if chinese_text == key:
				missing_chinese.append(test_group["type"] + "." + key)
	
	var completeness_en = (total_tested - missing_english.size()) * 100.0 / total_tested
	var completeness_zh = (total_tested - missing_chinese.size()) * 100.0 / total_tested
	
	result += "总测试条目 / Total Tested: %d\n" % total_tested
	result += "英文完整度 / English Completeness: %.1f%%\n" % completeness_en
	result += "中文完整度 / Chinese Completeness: %.1f%%\n" % completeness_zh
	
	if completeness_en >= 90 and completeness_zh >= 90:
		result += "[color=green]✅ 翻译完整性良好 / Translation completeness good[/color]"
	elif completeness_en >= 70 and completeness_zh >= 70:
		result += "[color=yellow]⚠️ 翻译完整性一般 / Translation completeness fair[/color]"
	else:
		result += "[color=red]❌ 翻译完整性差 / Translation completeness poor[/color]"
	
	return result
